declare class LiveTimeSyncService {
    private syncInterval;
    private readonly SYNC_INTERVAL_MS;
    /**
     * Start the live time synchronization service
     */
    start(): void;
    /**
     * Stop the live time synchronization service
     */
    stop(): void;
    /**
     * Sync system time to current computer time if live time is enabled
     */
    private syncSystemTime;
    /**
     * Force immediate sync (for manual triggers)
     */
    forceSyncNow(): Promise<boolean>;
    /**
     * Get sync status information
     */
    getSyncStatus(): Promise<any>;
}
export declare const liveTimeSyncService: LiveTimeSyncService;
export {};
