<Window x:Class="VMSAutoDeploy.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="VMS Auto-Deploy - Production Setup" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Icon="vms-deploy.ico">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2E7D32"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>
        
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,20"/>
        </Style>
        
        <Style x:Key="StatusStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="Padding" Value="10,5"/>
        </Style>
        
        <Style x:Key="SuccessStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusStyle}">
            <Setter Property="Foreground" Value="#2E7D32"/>
            <Setter Property="Background" Value="#E8F5E8"/>
        </Style>
        
        <Style x:Key="ErrorStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusStyle}">
            <Setter Property="Foreground" Value="#C62828"/>
            <Setter Property="Background" Value="#FFEBEE"/>
        </Style>
        
        <Style x:Key="InfoStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusStyle}">
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="Background" Value="#E3F2FD"/>
        </Style>
        
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="#2E7D32"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#9E9E9E"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Background="#F5F5F5" Margin="0,0,0,20">
            <TextBlock Text="VMS Production Auto-Deploy" Style="{StaticResource HeaderStyle}"/>
            <TextBlock Text="Automated Zero-Configuration Deployment System" Style="{StaticResource SubHeaderStyle}"/>
        </StackPanel>
        
        <!-- Status Panel -->
        <Border Grid.Row="1" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0">
            <StackPanel>
                <TextBlock Text="Deployment Status" FontSize="18" FontWeight="Bold" 
                          Foreground="#424242" HorizontalAlignment="Center" Margin="0,10"/>
                
                <TextBlock x:Name="StatusText" Text="Ready to deploy VMS system..." 
                          Style="{StaticResource InfoStyle}" HorizontalAlignment="Center"/>
                
                <ProgressBar x:Name="ProgressBar" Height="20" Margin="20,10" 
                            Minimum="0" Maximum="100" Value="0"/>
                
                <TextBlock x:Name="ProgressText" Text="Click 'Start Deployment' to begin" 
                          FontSize="12" Foreground="#757575" HorizontalAlignment="Center" Margin="0,5,0,15"/>
            </StackPanel>
        </Border>
        
        <!-- Log Output -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,20,20,0">
            <ScrollViewer x:Name="LogScrollViewer" VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="LogPanel" Margin="10">
                    <TextBlock Text="Deployment Log" FontSize="16" FontWeight="Bold" 
                              Foreground="#424242" Margin="0,0,0,10"/>
                    <TextBlock x:Name="InitialLogText" Text="System ready for deployment. All checks will be performed automatically." 
                              Style="{StaticResource InfoStyle}"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20">
            <Button x:Name="DeployButton" Content="🚀 Start Deployment" 
                   Style="{StaticResource PrimaryButton}" Click="DeployButton_Click"/>
            <Button x:Name="TestButton" Content="🔍 Test Configuration" 
                   Style="{StaticResource SecondaryButton}" Click="TestButton_Click"/>
            <Button x:Name="ExitButton" Content="❌ Exit" 
                   Style="{StaticResource SecondaryButton}" Click="ExitButton_Click"/>
        </StackPanel>
        
        <!-- Footer -->
        <Border Grid.Row="4" Background="#F5F5F5" Padding="20,10">
            <StackPanel>
                <TextBlock Text="VMS Production Deployment System v1.0" 
                          FontSize="12" Foreground="#757575" HorizontalAlignment="Center"/>
                <TextBlock x:Name="SystemInfoText" Text="Detecting system configuration..." 
                          FontSize="11" Foreground="#9E9E9E" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
