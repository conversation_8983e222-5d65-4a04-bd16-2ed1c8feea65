using System.Windows;

namespace VMSADMIN
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Set application properties
            Current.ShutdownMode = ShutdownMode.OnMainWindowClose;
            
            // Handle command line arguments
            if (e.Args.Length > 0)
            {
                foreach (var arg in e.Args)
                {
                    if (arg.ToLower() == "--startup")
                    {
                        // Started from Windows startup - could minimize to tray or show normally
                        // For now, show normally
                    }
                }
            }
        }
    }
}
