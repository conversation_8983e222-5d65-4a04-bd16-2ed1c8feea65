<Window x:Class="VMSClientEnhanced.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="VMS Client Enhanced - Network Discovery" 
        Height="280" Width="480"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
        
        <Style x:Key="StatusStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="Background" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Background="White" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15">
                <Border Background="#1565C0" Width="40" Height="40" CornerRadius="6" Margin="0,0,12,0">
                    <TextBlock Text="VMS" Foreground="White" FontSize="14" FontWeight="Bold" 
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="VMS Client Enhanced" Style="{StaticResource HeaderStyle}" Margin="0"/>
                    <TextBlock Text="Automatic Server Discovery" Style="{StaticResource SubHeaderStyle}" Margin="0"/>
                </StackPanel>
            </StackPanel>
        </StackPanel>
        
        <!-- Status Display -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0">
            <StackPanel Margin="20">
                <TextBlock x:Name="StatusText" Text="Initializing VMS Client..." 
                          Style="{StaticResource StatusStyle}" Foreground="#1565C0"/>
                
                <ProgressBar x:Name="ProgressBar" Height="6" Margin="15,8" 
                            Minimum="0" Maximum="100" Value="0" 
                            Background="#E0E0E0" Foreground="#1565C0"/>
                
                <TextBlock x:Name="DetailText" Text="Preparing to connect to VMS server..." 
                          FontSize="11" Foreground="#757575" HorizontalAlignment="Center" Margin="0,5"/>
                
                <Border Background="#E3F2FD" BorderBrush="#1565C0" BorderThickness="1" Padding="12" Margin="0,10,0,0">
                    <StackPanel>
                        <TextBlock Text="🌐 Network Status" FontWeight="Bold" FontSize="12" Foreground="#1565C0" Margin="0,0,0,5"/>
                        <TextBlock x:Name="NetworkStatusText" Text="Scanning for VMS server..." FontSize="10" Foreground="#424242" Margin="0,1"/>
                        <TextBlock x:Name="ServerInfoText" Text="Server: Not detected" FontSize="10" Foreground="#424242" Margin="0,1"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20,15">
            <Button x:Name="ConnectButton" Content="🚀 Connect to VMS" 
                   Style="{StaticResource ActionButton}" Click="ConnectButton_Click" IsEnabled="False"/>
            <Button x:Name="RefreshButton" Content="🔄 Refresh" 
                   Background="#757575" Foreground="White" FontSize="12" Padding="15,6" Margin="5" 
                   BorderThickness="0" Cursor="Hand" Click="RefreshButton_Click"/>
        </StackPanel>
        
        <!-- Footer -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15,8">
            <TextBlock x:Name="FooterStatusText" Text="VMS Client Enhanced v1.0 - Ready to connect" 
                      FontSize="10" Foreground="#9E9E9E" HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
