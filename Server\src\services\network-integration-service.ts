/**
 * Network Integration Service
 * Orchestrates all network services for bulletproof deployment
 * Provides unified interface for hybrid network management
 */

import { EventEmitter } from 'events';
import { hybridNetworkService } from './hybrid-network-service.js';
import { intelligentFallbackService } from './intelligent-fallback-service.js';
import { selfHealingNetworkMonitor } from './self-healing-network-monitor.js';
import { networkDiscoveryService } from './network-discovery-service.js';
import { portableDeploymentService } from './portable-deployment-service.js';
import { logger } from '../utils/logger.js';

export interface NetworkSystemStatus {
  overall: 'healthy' | 'degraded' | 'critical' | 'offline';
  components: {
    hybridNetwork: boolean;
    intelligentFallback: boolean;
    selfHealing: boolean;
    networkDiscovery: boolean;
    portableDeployment: boolean;
  };
  currentMode: 'static' | 'dynamic' | 'hybrid';
  serverURL: string;
  lastUpdate: Date;
  issues: string[];
  recommendations: string[];
}

export class NetworkIntegrationService extends EventEmitter {
  private _isInitialized = false;
  private _isRunning = false;
  private _startupTime: Date | null = null;

  constructor() {
    super();
    logger.info('🌐 Network Integration Service initialized');
  }

  /**
   * Initialize and start all network services
   */
  public async initialize(): Promise<void> {
    try {
      if (this._isInitialized) {
        logger.warn('Network Integration Service already initialized');
        return;
      }

      logger.info('🚀 Initializing Network Integration System...');
      const startTime = Date.now();

      // Phase 1: Initialize portable deployment (must be first)
      logger.info('📁 Phase 1: Initializing portable deployment...');
      await portableDeploymentService.initialize();

      // Phase 2: Start hybrid network management
      logger.info('🌐 Phase 2: Starting hybrid network management...');
      await hybridNetworkService.start();

      // Phase 3: Start intelligent fallback system
      logger.info('🧠 Phase 3: Starting intelligent fallback system...');
      await intelligentFallbackService.start();

      // Phase 4: Start self-healing monitor
      logger.info('🏥 Phase 4: Starting self-healing monitor...');
      await selfHealingNetworkMonitor.start();

      // Phase 5: Setup event listeners and integration
      this.setupEventListeners();

      const initTime = Date.now() - startTime;
      this._isInitialized = true;
      this._isRunning = true;
      this._startupTime = new Date();

      logger.info('✅ Network Integration System initialized successfully');
      logger.info(`⏱️ Initialization completed in ${initTime}ms`);
      
      // Log system status
      const status = await this.getSystemStatus();
      logger.info(`🎯 System Status: ${status.overall}`);
      logger.info(`🔗 Server URL: ${status.serverURL}`);
      logger.info(`⚙️ Network Mode: ${status.currentMode}`);

      // Emit initialization complete event
      this.emit('initialized', status);

    } catch (error) {
      logger.error('❌ Failed to initialize Network Integration System:', error);
      throw error;
    }
  }

  /**
   * Gracefully shutdown all network services
   */
  public async shutdown(): Promise<void> {
    try {
      if (!this._isRunning) {
        return;
      }

      logger.info('🛑 Shutting down Network Integration System...');

      // Stop services in reverse order
      await selfHealingNetworkMonitor.stop();
      await intelligentFallbackService.stop();
      await hybridNetworkService.stop();

      this._isRunning = false;
      logger.info('✅ Network Integration System shutdown complete');

    } catch (error) {
      logger.error('❌ Error during Network Integration System shutdown:', error);
    }
  }

  /**
   * Setup event listeners for service integration
   */
  private setupEventListeners(): void {
    // Listen to hybrid network events
    hybridNetworkService.on('networkChange', (event) => {
      logger.info(`🔄 Network change detected: ${event.type}`);
      this.emit('networkChange', event);
    });

    // Listen to fallback decisions
    intelligentFallbackService.on('fallbackDecision', (decision) => {
      logger.info(`🎯 Fallback decision: ${decision.action} (${decision.confidence}% confidence)`);
      this.emit('fallbackDecision', decision);
    });

    // Listen to healing actions
    selfHealingNetworkMonitor.on('healingAction', (action) => {
      logger.info(`🏥 Healing action: ${action.description} - ${action.success ? 'Success' : 'Failed'}`);
      this.emit('healingAction', action);
    });

    // Listen to health checks
    selfHealingNetworkMonitor.on('healthCheck', (result) => {
      logger.debug(`💓 Health check completed in ${result.duration}ms`);
      this.emit('healthCheck', result);
    });

    logger.info('📡 Event listeners configured');
  }

  /**
   * Get comprehensive system status
   */
  public async getSystemStatus(): Promise<NetworkSystemStatus> {
    try {
      const hybridConfig = hybridNetworkService.getNetworkConfiguration();
      const issues: string[] = [];
      const recommendations: string[] = [];

      // Check component status
      const components = {
        hybridNetwork: hybridNetworkService.isRunning(),
        intelligentFallback: intelligentFallbackService.isRunning(),
        selfHealing: selfHealingNetworkMonitor.isRunning(),
        networkDiscovery: networkDiscoveryService.isServiceRunning(),
        portableDeployment: portableDeploymentService.isInitialized()
      };

      // Determine overall health
      let overall: NetworkSystemStatus['overall'] = 'healthy';

      if (!components.hybridNetwork) {
        issues.push('Hybrid network service not running');
        overall = 'critical';
      }

      if (!components.networkDiscovery) {
        issues.push('Network discovery service not active');
        if (overall !== 'critical') overall = 'degraded';
      }

      if (!components.intelligentFallback) {
        issues.push('Intelligent fallback system not active');
        recommendations.push('Restart fallback system for optimal performance');
      }

      if (!components.selfHealing) {
        issues.push('Self-healing monitor not active');
        recommendations.push('Enable self-healing for proactive network management');
      }

      // Check network connectivity
      if (!hybridConfig.isStaticAvailable && !hybridConfig.isDynamicAvailable) {
        issues.push('No network connectivity available');
        overall = 'offline';
      } else if (!hybridConfig.isStaticAvailable) {
        issues.push('Static IP not available');
        recommendations.push('Check static IP configuration');
        if (overall === 'healthy') overall = 'degraded';
      }

      // Add recommendations based on current mode
      if (hybridConfig.mode === 'dynamic' && hybridConfig.isStaticAvailable) {
        recommendations.push('Consider switching to static mode for better performance');
      }

      return {
        overall,
        components,
        currentMode: hybridConfig.mode,
        serverURL: hybridNetworkService.getServerURL(),
        lastUpdate: new Date(),
        issues,
        recommendations
      };

    } catch (error) {
      logger.error('❌ Error getting system status:', error);
      return {
        overall: 'critical',
        components: {
          hybridNetwork: false,
          intelligentFallback: false,
          selfHealing: false,
          networkDiscovery: false,
          portableDeployment: false
        },
        currentMode: 'dynamic',
        serverURL: 'http://localhost:8080',
        lastUpdate: new Date(),
        issues: ['System status check failed'],
        recommendations: ['Restart network services']
      };
    }
  }

  /**
   * Get comprehensive system statistics
   */
  public async getSystemStats(): Promise<any> {
    try {
      return {
        uptime: this._startupTime ? Date.now() - this._startupTime.getTime() : 0,
        initialized: this._isInitialized,
        running: this._isRunning,
        networkConfig: hybridNetworkService.getNetworkConfiguration(),
        fallbackStats: intelligentFallbackService.getStats(),
        healingStats: selfHealingNetworkMonitor.getStats(),
        discoveryStats: networkDiscoveryService.getNetworkStats(),
        deploymentConfig: portableDeploymentService.getDeploymentConfig()
      };
    } catch (error) {
      logger.error('❌ Error getting system stats:', error);
      return null;
    }
  }

  /**
   * Force network mode switch
   */
  public async switchNetworkMode(mode: 'static' | 'dynamic'): Promise<boolean> {
    try {
      logger.info(`🔄 Manually switching to ${mode} mode...`);

      let success = false;
      if (mode === 'static') {
        success = await hybridNetworkService.switchToStaticMode();
      } else {
        success = await hybridNetworkService.switchToDynamicModeManual();
      }

      if (success) {
        logger.info(`✅ Successfully switched to ${mode} mode`);
        this.emit('modeSwitch', { mode, success: true });
      } else {
        logger.warn(`⚠️ Failed to switch to ${mode} mode`);
        this.emit('modeSwitch', { mode, success: false });
      }

      return success;

    } catch (error) {
      logger.error(`❌ Error switching to ${mode} mode:`, error);
      return false;
    }
  }

  /**
   * Trigger manual healing
   */
  public async triggerHealing(): Promise<boolean> {
    try {
      logger.info('🏥 Triggering manual healing...');
      
      // Force a health check which will trigger healing if needed
      await selfHealingNetworkMonitor.performHealthCheck();
      
      return true;
    } catch (error) {
      logger.error('❌ Error triggering manual healing:', error);
      return false;
    }
  }

  /**
   * Restart all network services
   */
  public async restartServices(): Promise<boolean> {
    try {
      logger.info('🔄 Restarting all network services...');

      await this.shutdown();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      await this.initialize();

      logger.info('✅ All network services restarted successfully');
      return true;

    } catch (error) {
      logger.error('❌ Error restarting network services:', error);
      return false;
    }
  }

  /**
   * Get current server URL
   */
  public getServerURL(): string {
    return hybridNetworkService.getServerURL();
  }

  /**
   * Check if system is ready
   */
  public isReady(): boolean {
    return this._isInitialized && this._isRunning;
  }

  /**
   * Get deployment information
   */
  public getDeploymentInfo(): any {
    const deploymentConfig = portableDeploymentService.getDeploymentConfig();
    const networkConfig = hybridNetworkService.getNetworkConfiguration();

    return {
      deploymentId: deploymentConfig?.deploymentId,
      deploymentTime: deploymentConfig?.deploymentTime,
      deploymentMode: deploymentConfig?.deploymentMode,
      vmsPath: deploymentConfig?.paths.vmsRoot,
      serverIP: networkConfig.currentIP,
      serverPort: networkConfig.port,
      networkMode: networkConfig.mode,
      staticIPAvailable: networkConfig.isStaticAvailable,
      dynamicIPAvailable: networkConfig.isDynamicAvailable
    };
  }

  /**
   * Export system configuration for backup/restore
   */
  public async exportConfiguration(): Promise<any> {
    try {
      const status = await this.getSystemStatus();
      const stats = await this.getSystemStats();
      const deploymentInfo = this.getDeploymentInfo();

      return {
        exportTime: new Date(),
        version: '1.0.0',
        status,
        stats,
        deploymentInfo,
        configuration: {
          hybrid: hybridNetworkService.getNetworkConfiguration(),
          fallback: intelligentFallbackService.getStats(),
          healing: selfHealingNetworkMonitor.getStats()
        }
      };

    } catch (error) {
      logger.error('❌ Error exporting configuration:', error);
      return null;
    }
  }
}

// Export singleton instance
export const networkIntegrationService = new NetworkIntegrationService();
