// VMS Server-Side Connection Resilience
// Production-grade connection handling with graceful disconnection

import { logger } from '../utils/logger';

interface DisconnectionTimer {
  userId: string;
  userName: string;
  department: string;
  timer: NodeJS.Timeout;
}

class ServerConnectionManager {
  private disconnectionTimers: Map<string, DisconnectionTimer> = new Map();
  private readonly GRACE_PERIOD = 10000; // 10 seconds grace period
  private readonly HEALTH_CHECK_THROTTLE = 30000; // 30 seconds between health checks per client
  private lastHealthCheck: Map<string, number> = new Map();

  // Handle graceful disconnection with reconnection grace period
  public handleDisconnection(
    userId: string, 
    userName: string, 
    department: string, 
    socketId: string,
    cleanupCallback: () => Promise<void>
  ) {
    const timerKey = `${userId}-${socketId}`;
    
    // Cancel any existing timer for this user/socket
    this.cancelDisconnectionTimer(timerKey);
    
    logger.info(`🔄 RESILIENCE: Starting ${this.GRACE_PERIOD/1000}s grace period for ${userName} (${userId})`);
    
    // Set up grace period timer
    const timer = setTimeout(async () => {
      logger.info(`⏰ RESILIENCE: Grace period expired for ${userName} (${userId}) - proceeding with cleanup`);
      
      try {
        await cleanupCallback();
        this.disconnectionTimers.delete(timerKey);
      } catch (error) {
        logger.error(`❌ RESILIENCE: Cleanup failed for ${userName}:`, error);
      }
    }, this.GRACE_PERIOD);
    
    // Store the timer
    this.disconnectionTimers.set(timerKey, {
      userId,
      userName,
      department,
      timer
    });
  }

  // Cancel disconnection timer (user reconnected)
  public cancelDisconnectionTimer(timerKey: string) {
    const existingTimer = this.disconnectionTimers.get(timerKey);
    if (existingTimer) {
      clearTimeout(existingTimer.timer);
      this.disconnectionTimers.delete(timerKey);
      logger.info(`✅ RESILIENCE: Cancelled disconnection timer for ${existingTimer.userName}`);
      return true;
    }
    return false;
  }

  // Handle user reconnection
  public handleReconnection(userId: string, userName: string, socketId: string) {
    const timerKey = `${userId}-${socketId}`;
    const cancelled = this.cancelDisconnectionTimer(timerKey);
    
    if (cancelled) {
      logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) reconnected during grace period`);
    } else {
      logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) connected (no pending disconnection)`);
    }
  }

  // Throttle health checks to prevent spam
  public shouldAllowHealthCheck(clientId: string): boolean {
    const now = Date.now();
    const lastCheck = this.lastHealthCheck.get(clientId) || 0;
    
    if (now - lastCheck >= this.HEALTH_CHECK_THROTTLE) {
      this.lastHealthCheck.set(clientId, now);
      return true;
    }
    
    return false;
  }

  // Clean up expired health check records
  public cleanupHealthCheckRecords() {
    const now = Date.now();
    const expiredThreshold = this.HEALTH_CHECK_THROTTLE * 2; // 2x throttle time
    
    for (const [clientId, lastCheck] of this.lastHealthCheck.entries()) {
      if (now - lastCheck > expiredThreshold) {
        this.lastHealthCheck.delete(clientId);
      }
    }
  }

  // Get connection statistics
  public getConnectionStats() {
    return {
      pendingDisconnections: this.disconnectionTimers.size,
      healthCheckClients: this.lastHealthCheck.size,
      gracePeriodMs: this.GRACE_PERIOD,
      healthCheckThrottleMs: this.HEALTH_CHECK_THROTTLE
    };
  }

  // Cleanup all timers (for server shutdown)
  public cleanup() {
    logger.info('🧹 RESILIENCE: Cleaning up all connection timers');
    
    for (const [timerKey, timerData] of this.disconnectionTimers.entries()) {
      clearTimeout(timerData.timer);
      logger.info(`🧹 RESILIENCE: Cleared timer for ${timerData.userName}`);
    }
    
    this.disconnectionTimers.clear();
    this.lastHealthCheck.clear();
  }
}

// Export singleton instance
export const serverConnectionManager = new ServerConnectionManager();

// Middleware to optimize health check endpoints
export function healthCheckMiddleware(req: any, res: any, next: any) {
  const clientId = req.ip + '-' + (req.headers['user-agent'] || 'unknown');
  
  // Check if this is a health check request
  if (req.path === '/api/health' || req.path === '/health') {
    // Throttle health checks per client
    if (!serverConnectionManager.shouldAllowHealthCheck(clientId)) {
      // Return cached response for throttled requests
      return res.status(200).json({
        status: 'healthy',
        cached: true,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  next();
}

// Periodic cleanup task
setInterval(() => {
  serverConnectionManager.cleanupHealthCheckRecords();
}, 60000); // Clean up every minute
