<Window x:Class="VMSADMIN.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="VMS-ADMIN Settings" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,0,3"/>
        </Style>
        
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderBrush" Value="#BDBDBD"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#9E9E9E"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="VMS-ADMIN Settings" Style="{StaticResource HeaderStyle}"/>
            <TextBlock Text="Configure VMS administration client preferences" 
                      FontSize="12" Foreground="#757575"/>
        </StackPanel>
        
        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Network Discovery Settings -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🌐 Network Discovery" Style="{StaticResource HeaderStyle}" FontSize="14"/>
                        
                        <TextBlock Text="Discovery Timeout (seconds):" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="DiscoveryTimeoutTextBox" Text="10" Style="{StaticResource InputStyle}" Width="100" HorizontalAlignment="Left"/>
                        
                        <TextBlock Text="Refresh Interval (seconds):" Style="{StaticResource LabelStyle}" Margin="0,10,0,3"/>
                        <TextBox x:Name="RefreshIntervalTextBox" Text="30" Style="{StaticResource InputStyle}" Width="100" HorizontalAlignment="Left"/>
                        
                        <CheckBox x:Name="AutoConnectCheckBox" Content="Auto-connect when server is found" 
                                 Margin="0,10,0,0" FontSize="12"/>
                    </StackPanel>
                </Border>
                
                <!-- Connection Settings -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🔗 Connection Settings" Style="{StaticResource HeaderStyle}" FontSize="14"/>
                        
                        <TextBlock Text="Manual Server IP (optional):" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="ManualServerIPTextBox" Style="{StaticResource InputStyle}"
                                Width="200" HorizontalAlignment="Left"/>
                        
                        <TextBlock Text="Admin Service Port:" Style="{StaticResource LabelStyle}" Margin="0,10,0,3"/>
                        <TextBox x:Name="AdminPortTextBox" Text="8081" Style="{StaticResource InputStyle}" Width="100" HorizontalAlignment="Left"/>
                        
                        <TextBlock Text="Connection Timeout (seconds):" Style="{StaticResource LabelStyle}" Margin="0,10,0,3"/>
                        <TextBox x:Name="ConnectionTimeoutTextBox" Text="10" Style="{StaticResource InputStyle}" Width="100" HorizontalAlignment="Left"/>
                    </StackPanel>
                </Border>
                
                <!-- Application Settings -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="⚙️ Application Settings" Style="{StaticResource HeaderStyle}" FontSize="14"/>
                        
                        <CheckBox x:Name="MinimizeToTrayCheckBox" Content="Minimize to system tray instead of closing" 
                                 FontSize="12" Margin="0,5"/>
                        
                        <CheckBox x:Name="StartWithWindowsCheckBox" Content="Start VMS-ADMIN with Windows" 
                                 FontSize="12" Margin="0,5"/>
                        
                        <CheckBox x:Name="ShowNotificationsCheckBox" Content="Show connection status notifications" 
                                 FontSize="12" Margin="0,5" IsChecked="True"/>
                        
                        <CheckBox x:Name="AutoCloseAfterConnectCheckBox" Content="Auto-close after opening dashboard" 
                                 FontSize="12" Margin="0,5" IsChecked="True"/>
                    </StackPanel>
                </Border>
                
                <!-- Advanced Settings -->
                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Padding="15">
                    <StackPanel>
                        <TextBlock Text="🔧 Advanced Settings" Style="{StaticResource HeaderStyle}" FontSize="14"/>
                        
                        <TextBlock Text="Log Level:" Style="{StaticResource LabelStyle}"/>
                        <ComboBox x:Name="LogLevelComboBox" Width="150" HorizontalAlignment="Left" SelectedIndex="1">
                            <ComboBoxItem Content="Debug"/>
                            <ComboBoxItem Content="Info"/>
                            <ComboBoxItem Content="Warning"/>
                            <ComboBoxItem Content="Error"/>
                        </ComboBox>
                        
                        <CheckBox x:Name="EnableLoggingCheckBox" Content="Enable detailed logging" 
                                 FontSize="12" Margin="0,10,0,5" IsChecked="True"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button x:Name="OpenLogFolderButton" Content="📁 Open Log Folder" 
                                   Style="{StaticResource SecondaryButton}" Click="OpenLogFolderButton_Click"/>
                            <Button x:Name="ClearLogsButton" Content="🗑️ Clear Logs" 
                                   Style="{StaticResource SecondaryButton}" Click="ClearLogsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="ResetButton" Content="🔄 Reset to Defaults" 
                   Style="{StaticResource SecondaryButton}" Click="ResetButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ Cancel" 
                   Style="{StaticResource SecondaryButton}" Click="CancelButton_Click"/>
            <Button x:Name="SaveButton" Content="✅ Save Settings" 
                   Style="{StaticResource PrimaryButton}" Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
