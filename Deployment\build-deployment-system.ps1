# VMS Automated Deployment System Build Script
# Builds all components for production deployment

param(
    [string]$Configuration = "Release",
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

Write-Host "🚀 VMS Automated Deployment System Build Script" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

$ErrorActionPreference = "Stop"
$VerbosePreference = if ($Verbose) { "Continue" } else { "SilentlyContinue" }

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir
$DeploymentDir = Join-Path $RootDir "Deployment"

Write-Host "📁 Root Directory: $RootDir" -ForegroundColor Cyan
Write-Host "📁 Deployment Directory: $DeploymentDir" -ForegroundColor Cyan

# Function to check if .NET SDK is installed
function Test-DotNetSDK {
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($dotnetVersion) {
            Write-Host "✅ .NET SDK found: $dotnetVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ .NET SDK not found" -ForegroundColor Red
        return $false
    }
    return $false
}

# Function to build Windows Service
function Build-WindowsService {
    Write-Host "🔧 Building VMS Windows Service..." -ForegroundColor Yellow
    
    $ServiceDir = Join-Path $DeploymentDir "VMS-Service"
    if (-not (Test-Path $ServiceDir)) {
        Write-Host "❌ Windows Service directory not found: $ServiceDir" -ForegroundColor Red
        return $false
    }

    Push-Location $ServiceDir
    try {
        if ($Clean) {
            Write-Host "🧹 Cleaning Windows Service..." -ForegroundColor Cyan
            dotnet clean --configuration $Configuration --verbosity quiet
        }

        Write-Host "🔨 Building Windows Service..." -ForegroundColor Cyan
        dotnet build --configuration $Configuration --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "📦 Publishing Windows Service..." -ForegroundColor Cyan
            dotnet publish --configuration $Configuration --output "bin\Publish" --verbosity quiet --self-contained true --runtime win-x64
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Windows Service built successfully" -ForegroundColor Green
                return $true
            }
        }
        
        Write-Host "❌ Windows Service build failed" -ForegroundColor Red
        return $false
    }
    finally {
        Pop-Location
    }
}

# Function to build Auto-Deploy application
function Build-AutoDeploy {
    Write-Host "🔧 Building VMS Auto-Deploy application..." -ForegroundColor Yellow
    
    $AutoDeployDir = Join-Path $DeploymentDir "VMS-Auto-Deploy"
    if (-not (Test-Path $AutoDeployDir)) {
        Write-Host "❌ Auto-Deploy directory not found: $AutoDeployDir" -ForegroundColor Red
        return $false
    }

    Push-Location $AutoDeployDir
    try {
        if ($Clean) {
            Write-Host "🧹 Cleaning Auto-Deploy application..." -ForegroundColor Cyan
            dotnet clean --configuration $Configuration --verbosity quiet
        }

        Write-Host "🔨 Building Auto-Deploy application..." -ForegroundColor Cyan
        dotnet build --configuration $Configuration --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "📦 Publishing Auto-Deploy application..." -ForegroundColor Cyan
            dotnet publish --configuration $Configuration --output "bin\Publish" --verbosity quiet --self-contained true --runtime win-x64
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Auto-Deploy application built successfully" -ForegroundColor Green
                return $true
            }
        }
        
        Write-Host "❌ Auto-Deploy application build failed" -ForegroundColor Red
        return $false
    }
    finally {
        Pop-Location
    }
}

# Function to build Enhanced VMS Client
function Build-EnhancedClient {
    Write-Host "🔧 Building Enhanced VMS Client..." -ForegroundColor Yellow
    
    $ClientDir = Join-Path $RootDir "Tools\VMS-Client-Enhanced"
    if (-not (Test-Path $ClientDir)) {
        Write-Host "❌ Enhanced Client directory not found: $ClientDir" -ForegroundColor Red
        return $false
    }

    # Check if there's a project file
    $ProjectFiles = Get-ChildItem -Path $ClientDir -Filter "*.csproj" -ErrorAction SilentlyContinue
    if ($ProjectFiles.Count -eq 0) {
        Write-Host "⚠️ No .csproj file found in Enhanced Client directory - skipping" -ForegroundColor Yellow
        return $true
    }

    Push-Location $ClientDir
    try {
        if ($Clean) {
            Write-Host "🧹 Cleaning Enhanced VMS Client..." -ForegroundColor Cyan
            dotnet clean --configuration $Configuration --verbosity quiet
        }

        Write-Host "🔨 Building Enhanced VMS Client..." -ForegroundColor Cyan
        dotnet build --configuration $Configuration --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "📦 Publishing Enhanced VMS Client..." -ForegroundColor Cyan
            dotnet publish --configuration $Configuration --output "bin\Publish" --verbosity quiet --self-contained true --runtime win-x64
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Enhanced VMS Client built successfully" -ForegroundColor Green
                return $true
            }
        }
        
        Write-Host "❌ Enhanced VMS Client build failed" -ForegroundColor Red
        return $false
    }
    finally {
        Pop-Location
    }
}

# Function to compile TypeScript services
function Build-TypeScriptServices {
    Write-Host "🔧 Building TypeScript network services..." -ForegroundColor Yellow
    
    $ServerDir = Join-Path $RootDir "Server"
    if (-not (Test-Path $ServerDir)) {
        Write-Host "❌ Server directory not found: $ServerDir" -ForegroundColor Red
        return $false
    }

    Push-Location $ServerDir
    try {
        # Check if npm is available
        try {
            $npmVersion = npm --version 2>$null
            Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ npm not found - cannot build TypeScript services" -ForegroundColor Red
            return $false
        }

        if ($Clean) {
            Write-Host "🧹 Cleaning TypeScript build..." -ForegroundColor Cyan
            if (Test-Path "dist") {
                Remove-Item -Path "dist" -Recurse -Force
            }
        }

        Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan
        npm install --silent

        Write-Host "🔨 Building TypeScript services..." -ForegroundColor Cyan
        npm run build --silent
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ TypeScript services built successfully" -ForegroundColor Green
            return $true
        }
        
        Write-Host "❌ TypeScript services build failed" -ForegroundColor Red
        return $false
    }
    finally {
        Pop-Location
    }
}

# Function to create deployment package
function Create-DeploymentPackage {
    Write-Host "📦 Creating deployment package..." -ForegroundColor Yellow
    
    $PackageDir = Join-Path $DeploymentDir "Package"
    $PackageDate = Get-Date -Format "yyyyMMdd-HHmmss"
    $PackageName = "VMS-Automated-Deployment-$PackageDate"
    $PackagePath = Join-Path $PackageDir $PackageName
    
    # Create package directory
    if (Test-Path $PackageDir) {
        Remove-Item -Path $PackageDir -Recurse -Force
    }
    New-Item -Path $PackagePath -ItemType Directory -Force | Out-Null
    
    # Copy Windows Service
    $ServiceSource = Join-Path $DeploymentDir "VMS-Service\bin\Publish"
    $ServiceDest = Join-Path $PackagePath "VMS-Service"
    if (Test-Path $ServiceSource) {
        Copy-Item -Path $ServiceSource -Destination $ServiceDest -Recurse -Force
        Write-Host "✅ Windows Service packaged" -ForegroundColor Green
    }
    
    # Copy Auto-Deploy application
    $AutoDeploySource = Join-Path $DeploymentDir "VMS-Auto-Deploy\bin\Publish"
    $AutoDeployDest = Join-Path $PackagePath "VMS-Auto-Deploy"
    if (Test-Path $AutoDeploySource) {
        Copy-Item -Path $AutoDeploySource -Destination $AutoDeployDest -Recurse -Force
        Write-Host "✅ Auto-Deploy application packaged" -ForegroundColor Green
    }
    
    # Copy Enhanced Client
    $ClientSource = Join-Path $RootDir "Tools\VMS-Client-Enhanced\bin\Publish"
    $ClientDest = Join-Path $PackagePath "VMS-Client-Enhanced"
    if (Test-Path $ClientSource) {
        Copy-Item -Path $ClientSource -Destination $ClientDest -Recurse -Force
        Write-Host "✅ Enhanced VMS Client packaged" -ForegroundColor Green
    }
    
    # Copy Server with built services
    $ServerSource = Join-Path $RootDir "Server"
    $ServerDest = Join-Path $PackagePath "Server"
    Copy-Item -Path $ServerSource -Destination $ServerDest -Recurse -Force -Exclude @("node_modules", ".env", "logs")
    Write-Host "✅ Server with network services packaged" -ForegroundColor Green
    
    # Copy existing Tools and Client
    $ToolsSource = Join-Path $RootDir "Tools"
    $ToolsDest = Join-Path $PackagePath "Tools"
    if (Test-Path $ToolsSource) {
        Copy-Item -Path $ToolsSource -Destination $ToolsDest -Recurse -Force
        Write-Host "✅ Tools packaged" -ForegroundColor Green
    }
    
    $ClientOriginalSource = Join-Path $RootDir "Client"
    $ClientOriginalDest = Join-Path $PackagePath "Client"
    if (Test-Path $ClientOriginalSource) {
        Copy-Item -Path $ClientOriginalSource -Destination $ClientOriginalDest -Recurse -Force
        Write-Host "✅ Original Client packaged" -ForegroundColor Green
    }
    
    # Create installation script
    $InstallScript = @"
@echo off
echo VMS Automated Deployment System Installation
echo ==========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Installing VMS Automated Deployment System...
echo.

REM Run the Auto-Deploy application
if exist "VMS-Auto-Deploy\VMS-Auto-Deploy.exe" (
    echo Starting VMS Auto-Deploy...
    start /wait "VMS Auto-Deploy" "VMS-Auto-Deploy\VMS-Auto-Deploy.exe"
) else (
    echo ERROR: VMS-Auto-Deploy.exe not found
    pause
    exit /b 1
)

echo.
echo Installation completed!
echo VMS system should now be running and accessible.
echo.
pause
"@
    
    $InstallScriptPath = Join-Path $PackagePath "INSTALL.bat"
    $InstallScript | Out-File -FilePath $InstallScriptPath -Encoding ASCII
    
    # Create README
    $ReadMe = @"
VMS Automated Deployment System
===============================

This package contains the complete VMS system with automated deployment capabilities.

INSTALLATION:
1. Copy this entire folder to the target server computer
2. Right-click on INSTALL.bat and select "Run as administrator"
3. Follow the on-screen instructions
4. The system will automatically configure network settings and start VMS

FEATURES:
- Automatic static IP assignment with dynamic fallback
- Zero-configuration deployment
- Self-healing network management
- Real-time server discovery for clients
- Production-ready monitoring and logging

COMPONENTS:
- VMS-Auto-Deploy.exe: Automated deployment application
- VMS-Service/: Windows Service for boot-time startup
- VMS-Client-Enhanced/: Enhanced client with network discovery
- Server/: VMS server with hybrid network services
- Tools/: Additional utilities and original VMS client

REQUIREMENTS:
- Windows 10/11 or Windows Server 2016+
- Administrator privileges for installation
- Network connectivity
- Node.js (will be detected/installed if needed)

SUPPORT:
- Check Server/logs/ for detailed logs
- Use VMS-Auto-Deploy.exe for system management
- All services start automatically on system boot

Generated: $(Get-Date)
Version: 1.0.0
"@
    
    $ReadMePath = Join-Path $PackagePath "README.txt"
    $ReadMe | Out-File -FilePath $ReadMePath -Encoding UTF8
    
    Write-Host "✅ Deployment package created: $PackagePath" -ForegroundColor Green
    Write-Host "📁 Package size: $((Get-ChildItem -Path $PackagePath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB) MB" -ForegroundColor Cyan
    
    return $PackagePath
}

# Main build process
try {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Cyan
    
    # Check .NET SDK
    if (-not (Test-DotNetSDK)) {
        Write-Host "❌ .NET SDK is required but not found" -ForegroundColor Red
        Write-Host "Please install .NET 6.0 SDK or later from https://dotnet.microsoft.com/download" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "✅ Prerequisites check passed" -ForegroundColor Green
    Write-Host ""
    
    # Build components
    $BuildResults = @{
        TypeScriptServices = Build-TypeScriptServices
        WindowsService = Build-WindowsService
        AutoDeploy = Build-AutoDeploy
        EnhancedClient = Build-EnhancedClient
    }
    
    Write-Host ""
    Write-Host "📊 Build Results:" -ForegroundColor Cyan
    Write-Host "=================" -ForegroundColor Cyan
    
    $SuccessCount = 0
    foreach ($Component in $BuildResults.Keys) {
        $Status = if ($BuildResults[$Component]) { "✅ SUCCESS"; $SuccessCount++ } else { "❌ FAILED" }
        $Color = if ($BuildResults[$Component]) { "Green" } else { "Red" }
        Write-Host "$Component`: $Status" -ForegroundColor $Color
    }
    
    Write-Host ""
    
    if ($SuccessCount -eq $BuildResults.Count) {
        Write-Host "🎉 All components built successfully!" -ForegroundColor Green
        
        # Create deployment package
        $PackagePath = Create-DeploymentPackage
        
        Write-Host ""
        Write-Host "🚀 VMS Automated Deployment System is ready!" -ForegroundColor Green
        Write-Host "📦 Deployment package: $PackagePath" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "NEXT STEPS:" -ForegroundColor Yellow
        Write-Host "1. Copy the deployment package to target server" -ForegroundColor White
        Write-Host "2. Run INSTALL.bat as Administrator" -ForegroundColor White
        Write-Host "3. Follow the automated deployment process" -ForegroundColor White
        Write-Host ""
        
    } else {
        Write-Host "⚠️ Some components failed to build" -ForegroundColor Yellow
        Write-Host "Check the error messages above and resolve issues before deployment" -ForegroundColor Yellow
        exit 1
    }
    
} catch {
    Write-Host "❌ Build process failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build script completed successfully!" -ForegroundColor Green
