{"name": "vms-production-server", "version": "5.0.0", "description": "VMS Production Server - Clean Architecture", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "start:prod": "set NODE_ENV=production && node dist/index.js", "dev": "set NODE_ENV=development && node --loader ts-node/esm src/index.ts", "build": "tsc", "build:clean": "rimraf dist && tsc", "build:prod": "npm run build:clean", "test": "jest", "clean": "<PERSON><PERSON><PERSON> dist", "setup": "npm install && npm run build:prod"}, "dependencies": {"@types/cookie-parser": "^1.4.9", "@types/express-session": "^1.18.2", "@types/uuid": "^10.0.0", "archiver": "^7.0.1", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-session": "^1.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.2", "node-fetch": "^3.3.2", "socket.io": "^4.7.5", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "winston": "^3.12.0", "zod": "^3.23.8"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.17.57", "cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.1.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["voucher-management", "production", "development", "dual-purpose"]}