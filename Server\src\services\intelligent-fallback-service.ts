/**
 * Intelligent Fallback Service
 * Implements seamless switching between static IP and network discovery modes
 * Provides smart decision-making for network mode transitions
 */

import { EventEmitter } from 'events';
import { hybridNetworkService, NetworkConfiguration, NetworkChangeEvent } from './hybrid-network-service.js';
import { networkDiscoveryService } from './network-discovery-service.js';
import { logger } from '../utils/logger.js';

export interface FallbackRule {
  id: string;
  name: string;
  condition: (config: NetworkConfiguration, event?: NetworkChangeEvent) => boolean;
  action: 'switch_to_static' | 'switch_to_dynamic' | 'maintain_current' | 'force_discovery';
  priority: number;
  cooldownMs: number;
  description: string;
}

export interface FallbackDecision {
  ruleId: string;
  ruleName: string;
  action: string;
  reason: string;
  confidence: number;
  timestamp: Date;
  networkConfig: NetworkConfiguration;
}

export interface FallbackStats {
  totalDecisions: number;
  staticToDynamic: number;
  dynamicToStatic: number;
  discoveryActivations: number;
  lastDecision: FallbackDecision | null;
  averageDecisionTime: number;
  successRate: number;
}

export class IntelligentFallbackService extends EventEmitter {
  private _isRunning = false;
  private _fallbackRules: FallbackRule[] = [];
  private _decisionHistory: FallbackDecision[] = [];
  private _ruleCooldowns: Map<string, number> = new Map();
  private _stats: FallbackStats = {
    totalDecisions: 0,
    staticToDynamic: 0,
    dynamicToStatic: 0,
    discoveryActivations: 0,
    lastDecision: null,
    averageDecisionTime: 0,
    successRate: 0
  };

  constructor() {
    super();
    this.initializeDefaultRules();
    logger.info('🧠 Intelligent Fallback Service initialized');
  }

  /**
   * Start intelligent fallback system
   */
  public async start(): Promise<void> {
    try {
      if (this._isRunning) {
        logger.warn('Intelligent Fallback Service is already running');
        return;
      }

      logger.info('🚀 Starting Intelligent Fallback System...');

      // Listen to network change events from hybrid network service
      hybridNetworkService.on('networkChange', this.handleNetworkChange.bind(this));

      // Start periodic evaluation
      this.startPeriodicEvaluation();

      this._isRunning = true;
      logger.info('✅ Intelligent Fallback System started successfully');

    } catch (error) {
      logger.error('❌ Failed to start Intelligent Fallback Service:', error);
      throw error;
    }
  }

  /**
   * Stop intelligent fallback system
   */
  public async stop(): Promise<void> {
    try {
      if (!this._isRunning) {
        return;
      }

      logger.info('🛑 Stopping Intelligent Fallback System...');

      // Remove event listeners
      hybridNetworkService.removeAllListeners('networkChange');

      this._isRunning = false;
      logger.info('✅ Intelligent Fallback System stopped');

    } catch (error) {
      logger.error('❌ Error stopping Intelligent Fallback Service:', error);
    }
  }

  /**
   * Initialize default fallback rules
   */
  private initializeDefaultRules(): void {
    this._fallbackRules = [
      {
        id: 'static_ip_failed',
        name: 'Static IP Connection Failed',
        condition: (config, event) => 
          event?.type === 'static_failed' && config.isDynamicAvailable,
        action: 'switch_to_dynamic',
        priority: 100,
        cooldownMs: 30000, // 30 seconds
        description: 'Switch to dynamic mode when static IP fails'
      },
      {
        id: 'static_ip_restored',
        name: 'Static IP Connection Restored',
        condition: (config, event) => 
          event?.type === 'static_restored' && config.mode === 'dynamic',
        action: 'switch_to_static',
        priority: 90,
        cooldownMs: 60000, // 1 minute
        description: 'Switch back to static mode when connection is restored'
      },
      {
        id: 'network_changed_major',
        name: 'Major Network Configuration Change',
        condition: (config, event) => {
          if (event?.type !== 'network_changed') return false;
          
          // Check if IP changed significantly (different subnet)
          const oldIP = event.oldConfig.currentIP;
          const newIP = event.newConfig.currentIP;
          
          if (!oldIP || !newIP) return false;
          
          const oldSubnet = oldIP.split('.').slice(0, 3).join('.');
          const newSubnet = newIP.split('.').slice(0, 3).join('.');
          
          return oldSubnet !== newSubnet;
        },
        action: 'force_discovery',
        priority: 80,
        cooldownMs: 120000, // 2 minutes
        description: 'Force discovery when network subnet changes'
      },
      {
        id: 'dynamic_mode_stable',
        name: 'Dynamic Mode Stable Performance',
        condition: (config) => 
          config.mode === 'dynamic' && 
          config.isDynamicAvailable && 
          !config.isStaticAvailable &&
          this.isNetworkStable(config),
        action: 'maintain_current',
        priority: 70,
        cooldownMs: 300000, // 5 minutes
        description: 'Maintain dynamic mode when stable and static unavailable'
      },
      {
        id: 'static_mode_preferred',
        name: 'Static Mode Preferred When Available',
        condition: (config) => 
          config.mode === 'dynamic' && 
          config.isStaticAvailable && 
          this.isNetworkStable(config),
        action: 'switch_to_static',
        priority: 60,
        cooldownMs: 180000, // 3 minutes
        description: 'Prefer static mode when available and network is stable'
      },
      {
        id: 'discovery_health_check',
        name: 'Network Discovery Health Check',
        condition: (config) => 
          !config.isDynamicAvailable && 
          config.mode === 'dynamic',
        action: 'force_discovery',
        priority: 50,
        cooldownMs: 60000, // 1 minute
        description: 'Force discovery restart when dynamic mode is unhealthy'
      }
    ];

    logger.info(`📋 Initialized ${this._fallbackRules.length} fallback rules`);
  }

  /**
   * Handle network change events
   */
  private async handleNetworkChange(event: NetworkChangeEvent): Promise<void> {
    try {
      logger.info(`🔄 Processing network change event: ${event.type}`);
      
      const startTime = Date.now();
      const decision = await this.evaluateNetworkChange(event);
      const decisionTime = Date.now() - startTime;

      if (decision) {
        await this.executeDecision(decision);
        this.updateStats(decision, decisionTime);
        this.emit('fallbackDecision', decision);
      }

    } catch (error) {
      logger.error('❌ Error handling network change:', error);
    }
  }

  /**
   * Evaluate network change and make fallback decision
   */
  private async evaluateNetworkChange(event: NetworkChangeEvent): Promise<FallbackDecision | null> {
    try {
      const config = hybridNetworkService.getNetworkConfiguration();
      
      // Find applicable rules
      const applicableRules = this._fallbackRules
        .filter(rule => this.isRuleApplicable(rule, config, event))
        .filter(rule => !this.isRuleInCooldown(rule.id))
        .sort((a, b) => b.priority - a.priority);

      if (applicableRules.length === 0) {
        logger.debug('No applicable fallback rules found');
        return null;
      }

      // Select the highest priority rule
      const selectedRule = applicableRules[0];
      
      // Calculate confidence based on rule priority and network conditions
      const confidence = this.calculateDecisionConfidence(selectedRule, config, event);

      const decision: FallbackDecision = {
        ruleId: selectedRule.id,
        ruleName: selectedRule.name,
        action: selectedRule.action,
        reason: selectedRule.description,
        confidence,
        timestamp: new Date(),
        networkConfig: config
      };

      // Set cooldown for the rule
      this._ruleCooldowns.set(selectedRule.id, Date.now() + selectedRule.cooldownMs);

      logger.info(`🎯 Fallback decision: ${selectedRule.name} -> ${selectedRule.action} (${confidence}% confidence)`);
      
      return decision;

    } catch (error) {
      logger.error('❌ Error evaluating network change:', error);
      return null;
    }
  }

  /**
   * Execute fallback decision
   */
  private async executeDecision(decision: FallbackDecision): Promise<void> {
    try {
      logger.info(`⚡ Executing fallback action: ${decision.action}`);

      let success = false;

      switch (decision.action) {
        case 'switch_to_static':
          success = await hybridNetworkService.switchToStaticMode();
          break;

        case 'switch_to_dynamic':
          success = await hybridNetworkService.switchToDynamicModeManual();
          break;

        case 'force_discovery':
          await this.forceNetworkDiscovery();
          success = true;
          break;

        case 'maintain_current':
          logger.info('💡 Maintaining current network mode');
          success = true;
          break;

        default:
          logger.warn(`⚠️ Unknown fallback action: ${decision.action}`);
          success = false;
      }

      if (success) {
        logger.info(`✅ Fallback action executed successfully: ${decision.action}`);
      } else {
        logger.warn(`⚠️ Fallback action failed: ${decision.action}`);
      }

      // Store decision in history
      this._decisionHistory.push(decision);
      
      // Keep only last 100 decisions
      if (this._decisionHistory.length > 100) {
        this._decisionHistory = this._decisionHistory.slice(-100);
      }

    } catch (error) {
      logger.error(`❌ Error executing fallback decision ${decision.action}:`, error);
    }
  }

  /**
   * Force network discovery restart
   */
  private async forceNetworkDiscovery(): Promise<void> {
    try {
      logger.info('🔄 Forcing network discovery restart...');
      
      // Stop and restart network discovery
      await networkDiscoveryService.stop();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      await networkDiscoveryService.start();
      
      logger.info('✅ Network discovery restarted');

    } catch (error) {
      logger.error('❌ Error forcing network discovery:', error);
    }
  }

  /**
   * Start periodic evaluation
   */
  private startPeriodicEvaluation(): void {
    setInterval(async () => {
      try {
        if (!this._isRunning) return;

        const config = hybridNetworkService.getNetworkConfiguration();
        const decision = await this.evaluatePeriodicConditions(config);
        
        if (decision) {
          await this.executeDecision(decision);
          this.updateStats(decision, 0);
          this.emit('fallbackDecision', decision);
        }

      } catch (error) {
        logger.error('❌ Error in periodic evaluation:', error);
      }
    }, 60000); // Every minute

    logger.info('⏰ Periodic evaluation started (60s intervals)');
  }

  /**
   * Evaluate periodic conditions
   */
  private async evaluatePeriodicConditions(config: NetworkConfiguration): Promise<FallbackDecision | null> {
    // Find rules that don't require events (periodic checks)
    const periodicRules = this._fallbackRules
      .filter(rule => this.isRuleApplicable(rule, config))
      .filter(rule => !this.isRuleInCooldown(rule.id))
      .sort((a, b) => b.priority - a.priority);

    if (periodicRules.length === 0) {
      return null;
    }

    const selectedRule = periodicRules[0];
    const confidence = this.calculateDecisionConfidence(selectedRule, config);

    // Only execute high-confidence periodic decisions
    if (confidence < 70) {
      return null;
    }

    this._ruleCooldowns.set(selectedRule.id, Date.now() + selectedRule.cooldownMs);

    return {
      ruleId: selectedRule.id,
      ruleName: selectedRule.name,
      action: selectedRule.action,
      reason: `Periodic check: ${selectedRule.description}`,
      confidence,
      timestamp: new Date(),
      networkConfig: config
    };
  }

  /**
   * Check if rule is applicable
   */
  private isRuleApplicable(rule: FallbackRule, config: NetworkConfiguration, event?: NetworkChangeEvent): boolean {
    try {
      return rule.condition(config, event);
    } catch (error) {
      logger.warn(`⚠️ Error evaluating rule ${rule.id}:`, error);
      return false;
    }
  }

  /**
   * Check if rule is in cooldown
   */
  private isRuleInCooldown(ruleId: string): boolean {
    const cooldownEnd = this._ruleCooldowns.get(ruleId);
    return cooldownEnd ? Date.now() < cooldownEnd : false;
  }

  /**
   * Calculate decision confidence
   */
  private calculateDecisionConfidence(rule: FallbackRule, config: NetworkConfiguration, event?: NetworkChangeEvent): number {
    let confidence = rule.priority; // Base confidence from rule priority

    // Adjust based on network stability
    if (this.isNetworkStable(config)) {
      confidence += 10;
    } else {
      confidence -= 10;
    }

    // Adjust based on event type
    if (event) {
      switch (event.type) {
        case 'static_failed':
        case 'static_restored':
          confidence += 20; // High confidence for clear state changes
          break;
        case 'network_changed':
          confidence += 10; // Medium confidence for network changes
          break;
        default:
          confidence += 5; // Low confidence for other events
      }
    }

    // Adjust based on historical success rate
    const recentDecisions = this._decisionHistory.slice(-10);
    const successRate = recentDecisions.length > 0 
      ? recentDecisions.filter(d => d.ruleId === rule.id).length / recentDecisions.length 
      : 0.5;
    
    confidence += (successRate - 0.5) * 20;

    // Clamp to 0-100 range
    return Math.max(0, Math.min(100, confidence));
  }

  /**
   * Check if network is stable
   */
  private isNetworkStable(config: NetworkConfiguration): boolean {
    const timeSinceLastChange = Date.now() - config.lastNetworkChange.getTime();
    return timeSinceLastChange > 120000; // Stable if no changes for 2 minutes
  }

  /**
   * Update statistics
   */
  private updateStats(decision: FallbackDecision, decisionTime: number): void {
    this._stats.totalDecisions++;
    this._stats.lastDecision = decision;
    
    // Update average decision time
    this._stats.averageDecisionTime = 
      (this._stats.averageDecisionTime * (this._stats.totalDecisions - 1) + decisionTime) / 
      this._stats.totalDecisions;

    // Count action types
    switch (decision.action) {
      case 'switch_to_static':
        this._stats.dynamicToStatic++;
        break;
      case 'switch_to_dynamic':
        this._stats.staticToDynamic++;
        break;
      case 'force_discovery':
        this._stats.discoveryActivations++;
        break;
    }

    // Calculate success rate (simplified - based on confidence)
    const recentDecisions = this._decisionHistory.slice(-20);
    const avgConfidence = recentDecisions.reduce((sum, d) => sum + d.confidence, 0) / recentDecisions.length;
    this._stats.successRate = avgConfidence || 0;
  }

  /**
   * Get fallback statistics
   */
  public getStats(): FallbackStats {
    return { ...this._stats };
  }

  /**
   * Get decision history
   */
  public getDecisionHistory(limit: number = 50): FallbackDecision[] {
    return this._decisionHistory.slice(-limit);
  }

  /**
   * Add custom fallback rule
   */
  public addCustomRule(rule: FallbackRule): void {
    this._fallbackRules.push(rule);
    this._fallbackRules.sort((a, b) => b.priority - a.priority);
    logger.info(`📋 Added custom fallback rule: ${rule.name}`);
  }

  /**
   * Remove fallback rule
   */
  public removeRule(ruleId: string): boolean {
    const index = this._fallbackRules.findIndex(rule => rule.id === ruleId);
    if (index !== -1) {
      this._fallbackRules.splice(index, 1);
      logger.info(`🗑️ Removed fallback rule: ${ruleId}`);
      return true;
    }
    return false;
  }

  /**
   * Check if service is running
   */
  public isRunning(): boolean {
    return this._isRunning;
  }
}

// Export singleton instance
export const intelligentFallbackService = new IntelligentFallbackService();
