"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = authenticate;
exports.authorize = authorize;
exports.attachDatabase = attachDatabase;
const logger_js_1 = require("../utils/logger.js");
// LAN-optimized authentication middleware - session-based (no JWT tokens)
async function authenticate(req, res, next) {
    try {
        logger_js_1.logger.info(`🔐 LAN Authentication for: ${req.method} ${req.url}`);
        // For LAN deployment, use simple session-based authentication
        // Check for session cookie or header (FIXED: Check correct cookie name)
        const sessionId = req.headers['x-session-id'] || req.cookies?.vms_session_id || req.cookies?.sessionId;
        if (!sessionId) {
            logger_js_1.logger.warn('🔐 No session ID found - LAN mode requires login');
            return res.status(401).json({ error: 'Authentication required' });
        }
        try {
            // Import query function here to avoid circular dependencies
            const { query } = await import('../database/db.js');
            // Check if session exists and is active
            const sessions = await query('SELECT * FROM active_sessions WHERE id = ? AND is_active = TRUE', [sessionId]);
            if (sessions.length === 0) {
                logger_js_1.logger.warn(`Invalid session ID: ${sessionId}`);
                return res.status(401).json({ error: 'Invalid session' });
            }
            const session = sessions[0];
            // PRODUCTION FIX: Align with frontend - 8 hours for work day
            const sessionAge = Date.now() - new Date(session.session_start).getTime();
            const maxAge = 8 * 60 * 60 * 1000; // 8 hours (aligned with frontend)
            if (sessionAge > maxAge) {
                // Expire the session
                await query('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
                logger_js_1.logger.warn(`Session expired after 8 hours: ${sessionId}`);
                return res.status(401).json({ error: 'Session expired after 8 hours' });
            }
            // PRODUCTION OPTIMIZED: Check inactivity timeout (30 minutes)
            const inactivityAge = Date.now() - new Date(session.last_activity).getTime();
            const inactivityTimeout = 30 * 60 * 1000; // 30 minutes (optimal balance)
            if (inactivityAge > inactivityTimeout) {
                // Expire due to inactivity
                await query('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
                logger_js_1.logger.warn(`Session expired due to 30-minute inactivity: ${sessionId}`);
                return res.status(401).json({ error: 'Session expired due to inactivity (30 minutes)' });
            }
            // Update last activity
            await query('UPDATE active_sessions SET last_activity = NOW() WHERE id = ?', [sessionId]);
            // Get user data
            const users = await query('SELECT * FROM users WHERE id = ?', [session.user_id]);
            if (users.length === 0) {
                logger_js_1.logger.warn(`User not found for session: ${sessionId}`);
                return res.status(401).json({ error: 'User not found' });
            }
            const user = users[0];
            // Attach user data to request
            req.user = {
                id: user.id,
                name: user.name,
                department: user.department,
                role: user.role,
                sessionId: sessionId
            };
            logger_js_1.logger.debug(`LAN Authentication successful for user: ${user.name}`);
            next();
        }
        catch (dbError) {
            logger_js_1.logger.error('Session validation error:', dbError);
            return res.status(500).json({ error: 'Authentication error' });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Authentication middleware error:', error);
        return res.status(500).json({ error: 'Authentication error' });
    }
}
// Role-based authorization middleware
function authorize(roles) {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }
        next();
    };
}
// Database middleware - attach database to request
async function attachDatabase(req, res, next) {
    try {
        // Import the database functions
        const { query, getTransaction } = await import('../database/db.js');
        req.db = {
            execute: query, // Use the query function which handles execute internally
            query: query,
            getTransaction: getTransaction
        };
        next();
    }
    catch (error) {
        logger_js_1.logger.error('Database middleware error:', error);
        res.status(500).json({ error: 'Database connection error' });
    }
}
//# sourceMappingURL=auth.js.map