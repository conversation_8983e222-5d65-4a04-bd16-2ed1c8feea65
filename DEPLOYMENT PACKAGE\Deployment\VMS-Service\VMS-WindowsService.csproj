<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>VMSWindowsService</RootNamespace>
    <AssemblyName>VMS-WindowsService</AssemblyName>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>vms-service.ico</ApplicationIcon>
    <AssemblyTitle>VMS Production Windows Service</AssemblyTitle>
    <AssemblyDescription>Automated VMS system management service for production deployment</AssemblyDescription>
    <AssemblyCompany>VMS Production Team</AssemblyCompany>
    <AssemblyProduct>VMS Production Service</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2025 VMS Production Team</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="7.0.1" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Configuration.Install" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="vms-service.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
