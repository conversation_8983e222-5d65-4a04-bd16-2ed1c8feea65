// Real-Time Voucher Updates Hook
// Ensures voucher lists update immediately after sync without breaking existing functionality

import { useEffect, useRef, useState } from 'react';
import { useAppStore } from '@/lib/store';
import { realTimeSyncManager } from '@/lib/real-time-sync-manager';

interface UseRealTimeVouchersOptions {
  componentId: string;
  department?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useRealTimeVouchers(options: UseRealTimeVouchersOptions) {
  const { componentId, department, autoRefresh = true, refreshInterval = 30000 } = options;
  
  const vouchers = useAppStore(state => state.vouchers);
  const fetchVouchers = useAppStore(state => state.fetchVouchers);
  const isOnline = useAppStore(state => state.isOnline);
  
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const componentMountedRef = useRef<boolean>(true);

  // Filter vouchers by department if specified
  const filteredVouchers = department 
    ? vouchers.filter(v => v.department === department)
    : vouchers;

  // Refresh function
  const refreshVouchers = async (source: string = 'manual') => {
    if (!componentMountedRef.current || isRefreshing) return;
    
    console.log(`🔄 [${componentId}] Refreshing vouchers (source: ${source})`);
    setIsRefreshing(true);
    
    try {
      await fetchVouchers(department);
      setLastUpdateTime(Date.now());
      console.log(`✅ [${componentId}] Vouchers refreshed successfully`);
    } catch (error) {
      console.error(`❌ [${componentId}] Failed to refresh vouchers:`, error);
    } finally {
      if (componentMountedRef.current) {
        setIsRefreshing(false);
      }
    }
  };

  // Handle sync completion
  const handleSyncComplete = async (event: CustomEvent) => {
    const { syncedCount } = event.detail;
    if (syncedCount > 0) {
      console.log(`🎯 [${componentId}] Sync completed with ${syncedCount} operations - refreshing`);
      await refreshVouchers('sync-complete');
    }
  };

  // Handle voucher updates
  const handleVoucherUpdate = async (event: CustomEvent) => {
    console.log(`📄 [${componentId}] Voucher update received:`, event.detail);
    await refreshVouchers('voucher-update');
  };

  // Handle connection restoration
  const handleConnectionRestored = async () => {
    console.log(`🌐 [${componentId}] Connection restored - refreshing vouchers`);
    // Wait a moment for sync to complete
    setTimeout(() => refreshVouchers('connection-restored'), 3000);
  };

  // Setup event listeners and refresh callback
  useEffect(() => {
    console.log(`🔧 [${componentId}] Setting up real-time voucher updates`);
    
    // Register refresh callback with sync manager
    const refreshCallback = () => refreshVouchers('sync-manager');
    realTimeSyncManager.registerRefreshCallback(componentId, refreshCallback);
    
    // Event listeners
    window.addEventListener('vms-sync-complete', handleSyncComplete);
    window.addEventListener('vms-vouchers-updated', handleVoucherUpdate);
    window.addEventListener('vms-voucher-synced', handleVoucherUpdate);
    window.addEventListener('online', handleConnectionRestored);
    
    // Auto-refresh interval if enabled
    if (autoRefresh && isOnline) {
      refreshTimeoutRef.current = setInterval(() => {
        refreshVouchers('auto-refresh');
      }, refreshInterval);
    }
    
    // Cleanup function
    return () => {
      console.log(`🧹 [${componentId}] Cleaning up real-time voucher updates`);
      
      // Unregister from sync manager
      realTimeSyncManager.unregisterRefreshCallback(componentId);
      
      // Remove event listeners
      window.removeEventListener('vms-sync-complete', handleSyncComplete);
      window.removeEventListener('vms-vouchers-updated', handleVoucherUpdate);
      window.removeEventListener('vms-voucher-synced', handleVoucherUpdate);
      window.removeEventListener('online', handleConnectionRestored);
      
      // Clear auto-refresh interval
      if (refreshTimeoutRef.current) {
        clearInterval(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
      
      componentMountedRef.current = false;
    };
  }, [componentId, department, autoRefresh, refreshInterval, isOnline]);

  // Handle online/offline state changes
  useEffect(() => {
    if (isOnline && autoRefresh) {
      // Start auto-refresh when coming online
      if (!refreshTimeoutRef.current) {
        refreshTimeoutRef.current = setInterval(() => {
          refreshVouchers('auto-refresh');
        }, refreshInterval);
      }
      
      // Refresh immediately when coming online
      refreshVouchers('online');
    } else {
      // Stop auto-refresh when offline
      if (refreshTimeoutRef.current) {
        clearInterval(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    }
  }, [isOnline, autoRefresh, refreshInterval]);

  // Force refresh function for manual use
  const forceRefresh = () => refreshVouchers('force');

  // Get refresh status
  const getRefreshStatus = () => ({
    lastUpdateTime,
    isRefreshing,
    voucherCount: filteredVouchers.length,
    isOnline,
    autoRefreshEnabled: autoRefresh && isOnline
  });

  return {
    vouchers: filteredVouchers,
    refreshVouchers: forceRefresh,
    isRefreshing,
    lastUpdateTime,
    getRefreshStatus
  };
}

// Specialized hook for department-specific vouchers
export function useDepartmentVouchers(department: string, componentId?: string) {
  return useRealTimeVouchers({
    componentId: componentId || `department-${department}`,
    department,
    autoRefresh: true,
    refreshInterval: 30000
  });
}

// Specialized hook for all vouchers
export function useAllVouchers(componentId?: string) {
  return useRealTimeVouchers({
    componentId: componentId || 'all-vouchers',
    autoRefresh: true,
    refreshInterval: 30000
  });
}
