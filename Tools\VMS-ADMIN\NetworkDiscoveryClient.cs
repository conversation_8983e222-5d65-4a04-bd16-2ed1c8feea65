using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace VMSClientEnhanced
{
    /// <summary>
    /// Network Discovery Client for VMS Server Auto-Discovery
    /// Handles dynamic IP discovery and automatic server connection
    /// </summary>
    public class NetworkDiscoveryClient
    {
        private UdpClient _discoveryClient;
        private UdpClient _broadcastListener;
        private Timer _discoveryTimer;
        private bool _isRunning = false;
        private readonly object _lockObject = new object();

        public event EventHandler<ServerDiscoveredEventArgs> ServerDiscovered;
        public event EventHandler<string> LogMessage;

        private readonly NetworkDiscoveryConfig _config = new NetworkDiscoveryConfig
        {
            BroadcastPort = 8081,
            DiscoveryPort = 8082,
            DiscoveryInterval = 10000, // 10 seconds
            DiscoveryTimeout = 5000,   // 5 seconds
            MaxRetries = 3
        };

        private readonly List<DiscoveredServer> _discoveredServers = new List<DiscoveredServer>();

        /// <summary>
        /// Start network discovery
        /// </summary>
        public async Task StartDiscoveryAsync()
        {
            try
            {
                if (_isRunning)
                {
                    LogMessage?.Invoke(this, "Network discovery is already running");
                    return;
                }

                LogMessage?.Invoke(this, "🌐 Starting VMS server discovery...");

                // Start listening for server announcements
                await StartBroadcastListenerAsync();

                // Start active discovery
                await StartActiveDiscoveryAsync();

                // Schedule periodic discovery
                _discoveryTimer = new Timer(PeriodicDiscovery, null, 
                    TimeSpan.FromSeconds(2), TimeSpan.FromMilliseconds(_config.DiscoveryInterval));

                _isRunning = true;
                LogMessage?.Invoke(this, "✅ Network discovery started successfully");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start network discovery: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop network discovery
        /// </summary>
        public void StopDiscovery()
        {
            try
            {
                if (!_isRunning)
                    return;

                LogMessage?.Invoke(this, "🛑 Stopping network discovery...");

                _discoveryTimer?.Dispose();
                _discoveryTimer = null;

                _discoveryClient?.Close();
                _discoveryClient?.Dispose();
                _discoveryClient = null;

                _broadcastListener?.Close();
                _broadcastListener?.Dispose();
                _broadcastListener = null;

                _isRunning = false;
                LogMessage?.Invoke(this, "✅ Network discovery stopped");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error stopping network discovery: {ex.Message}");
            }
        }

        /// <summary>
        /// Start listening for server broadcast announcements
        /// </summary>
        private async Task StartBroadcastListenerAsync()
        {
            try
            {
                _broadcastListener = new UdpClient(_config.BroadcastPort);
                _broadcastListener.EnableBroadcast = true;

                LogMessage?.Invoke(this, $"📡 Listening for server announcements on port {_config.BroadcastPort}");

                // Start listening in background
                _ = Task.Run(async () =>
                {
                    while (_isRunning && _broadcastListener != null)
                    {
                        try
                        {
                            var result = await _broadcastListener.ReceiveAsync();
                            await ProcessServerAnnouncementAsync(result.Buffer, result.RemoteEndPoint);
                        }
                        catch (ObjectDisposedException)
                        {
                            // Expected when stopping
                            break;
                        }
                        catch (Exception ex)
                        {
                            LogMessage?.Invoke(this, $"⚠️ Error receiving broadcast: {ex.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start broadcast listener: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start active discovery by sending discovery requests
        /// </summary>
        private async Task StartActiveDiscoveryAsync()
        {
            try
            {
                _discoveryClient = new UdpClient();
                _discoveryClient.EnableBroadcast = true;

                LogMessage?.Invoke(this, "🔍 Starting active server discovery");
                await SendDiscoveryRequestAsync();
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start active discovery: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Send discovery request to find VMS servers
        /// </summary>
        private async Task SendDiscoveryRequestAsync()
        {
            try
            {
                var request = new
                {
                    type = "VMS_DISCOVERY_REQUEST",
                    clientId = Environment.MachineName,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    version = "1.0.0"
                };

                var requestJson = JsonSerializer.Serialize(request);
                var requestBytes = Encoding.UTF8.GetBytes(requestJson);

                // Send to broadcast addresses
                var broadcastAddresses = GetBroadcastAddresses();
                
                foreach (var address in broadcastAddresses)
                {
                    try
                    {
                        await _discoveryClient.SendAsync(requestBytes, requestBytes.Length, 
                            new IPEndPoint(IPAddress.Parse(address), _config.DiscoveryPort));
                        
                        LogMessage?.Invoke(this, $"📤 Discovery request sent to {address}:{_config.DiscoveryPort}");
                    }
                    catch (Exception ex)
                    {
                        LogMessage?.Invoke(this, $"⚠️ Failed to send discovery to {address}: {ex.Message}");
                    }
                }

                // Listen for responses
                _ = Task.Run(async () =>
                {
                    var timeout = DateTime.UtcNow.AddMilliseconds(_config.DiscoveryTimeout);
                    
                    while (DateTime.UtcNow < timeout && _isRunning)
                    {
                        try
                        {
                            var result = await _discoveryClient.ReceiveAsync();
                            await ProcessDiscoveryResponseAsync(result.Buffer, result.RemoteEndPoint);
                        }
                        catch (Exception ex)
                        {
                            LogMessage?.Invoke(this, $"⚠️ Error receiving discovery response: {ex.Message}");
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Error sending discovery request: {ex.Message}");
            }
        }

        /// <summary>
        /// Process server announcement from broadcast
        /// </summary>
        private async Task ProcessServerAnnouncementAsync(byte[] data, IPEndPoint remoteEndPoint)
        {
            try
            {
                var json = Encoding.UTF8.GetString(data);
                var announcement = JsonSerializer.Deserialize<ServerAnnouncementMessage>(json);

                if (announcement?.type == "VMS_SERVER_ANNOUNCEMENT" && announcement.server != null)
                {
                    var server = announcement.server;
                    LogMessage?.Invoke(this, $"📡 Server announcement received from {server.serverIP}:{server.serverPort}");
                    
                    await AddOrUpdateDiscoveredServerAsync(server, "broadcast");
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error processing server announcement: {ex.Message}");
            }
        }

        /// <summary>
        /// Process discovery response
        /// </summary>
        private async Task ProcessDiscoveryResponseAsync(byte[] data, IPEndPoint remoteEndPoint)
        {
            try
            {
                var json = Encoding.UTF8.GetString(data);
                var response = JsonSerializer.Deserialize<DiscoveryResponseMessage>(json);

                if (response?.type == "VMS_DISCOVERY_RESPONSE" && response.server != null)
                {
                    var server = response.server;
                    LogMessage?.Invoke(this, $"🔍 Discovery response received from {server.serverIP}:{server.serverPort}");
                    
                    await AddOrUpdateDiscoveredServerAsync(server, "discovery");
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error processing discovery response: {ex.Message}");
            }
        }

        /// <summary>
        /// Add or update discovered server
        /// </summary>
        private async Task AddOrUpdateDiscoveredServerAsync(ServerInfo server, string discoveryMethod)
        {
            try
            {
                lock (_lockObject)
                {
                    var existing = _discoveredServers.FirstOrDefault(s => 
                        s.ServerIP == server.serverIP && s.ServerPort == server.serverPort);

                    if (existing != null)
                    {
                        // Update existing server
                        existing.LastSeen = DateTime.UtcNow;
                        existing.DiscoveryMethod = discoveryMethod;
                        existing.Version = server.version;
                        existing.Capabilities = server.capabilities?.ToList() ?? new List<string>();
                    }
                    else
                    {
                        // Add new server
                        var discoveredServer = new DiscoveredServer
                        {
                            ServiceName = server.serviceName,
                            ServerIP = server.serverIP,
                            ServerPort = server.serverPort,
                            Version = server.version,
                            Capabilities = server.capabilities?.ToList() ?? new List<string>(),
                            DeploymentMode = server.deploymentMode,
                            DiscoveryMethod = discoveryMethod,
                            FirstSeen = DateTime.UtcNow,
                            LastSeen = DateTime.UtcNow
                        };

                        _discoveredServers.Add(discoveredServer);
                        
                        LogMessage?.Invoke(this, $"✅ New VMS server discovered: {server.serverIP}:{server.serverPort}");
                        
                        // Notify about new server
                        ServerDiscovered?.Invoke(this, new ServerDiscoveredEventArgs(discoveredServer));
                    }
                }

                // Test server connectivity
                await TestServerConnectivityAsync(server.serverIP, server.serverPort);
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error adding/updating discovered server: {ex.Message}");
            }
        }

        /// <summary>
        /// Test server connectivity
        /// </summary>
        private async Task TestServerConnectivityAsync(string serverIP, int serverPort)
        {
            try
            {
                using (var client = new System.Net.Http.HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    var response = await client.GetAsync($"http://{serverIP}:{serverPort}/health");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        LogMessage?.Invoke(this, $"✅ Server {serverIP}:{serverPort} is accessible and healthy");
                    }
                    else
                    {
                        LogMessage?.Invoke(this, $"⚠️ Server {serverIP}:{serverPort} responded but not healthy");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Server {serverIP}:{serverPort} connectivity test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Periodic discovery timer callback
        /// </summary>
        private async void PeriodicDiscovery(object state)
        {
            try
            {
                if (!_isRunning)
                    return;

                // Clean up old servers (not seen for 2 minutes)
                lock (_lockObject)
                {
                    var cutoff = DateTime.UtcNow.AddMinutes(-2);
                    var oldServers = _discoveredServers.Where(s => s.LastSeen < cutoff).ToList();
                    
                    foreach (var server in oldServers)
                    {
                        _discoveredServers.Remove(server);
                        LogMessage?.Invoke(this, $"🗑️ Removed stale server: {server.ServerIP}:{server.ServerPort}");
                    }
                }

                // Send new discovery request
                await SendDiscoveryRequestAsync();
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error in periodic discovery: {ex.Message}");
            }
        }

        /// <summary>
        /// Get broadcast addresses for current network interfaces
        /// </summary>
        private List<string> GetBroadcastAddresses()
        {
            var addresses = new List<string>();
            
            try
            {
                foreach (var ni in System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                        (ni.NetworkInterfaceType == System.Net.NetworkInformation.NetworkInterfaceType.Ethernet ||
                         ni.NetworkInterfaceType == System.Net.NetworkInformation.NetworkInterfaceType.Wireless80211))
                    {
                        foreach (var ip in ni.GetIPProperties().UnicastAddresses)
                        {
                            if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                // Calculate broadcast address
                                var ipBytes = ip.Address.GetAddressBytes();
                                var maskBytes = ip.IPv4Mask.GetAddressBytes();
                                
                                var broadcastBytes = new byte[4];
                                for (int i = 0; i < 4; i++)
                                {
                                    broadcastBytes[i] = (byte)(ipBytes[i] | (255 - maskBytes[i]));
                                }
                                
                                var broadcastAddress = new IPAddress(broadcastBytes).ToString();
                                if (!addresses.Contains(broadcastAddress))
                                {
                                    addresses.Add(broadcastAddress);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error getting broadcast addresses: {ex.Message}");
            }

            // Add fallback broadcast address
            if (addresses.Count == 0)
            {
                addresses.Add("***************");
            }

            return addresses;
        }

        /// <summary>
        /// Get all discovered servers
        /// </summary>
        public List<DiscoveredServer> GetDiscoveredServers()
        {
            lock (_lockObject)
            {
                return new List<DiscoveredServer>(_discoveredServers);
            }
        }

        /// <summary>
        /// Get the best server (most recently seen, healthy)
        /// </summary>
        public DiscoveredServer GetBestServer()
        {
            lock (_lockObject)
            {
                return _discoveredServers
                    .Where(s => s.LastSeen > DateTime.UtcNow.AddMinutes(-1))
                    .OrderByDescending(s => s.LastSeen)
                    .FirstOrDefault();
            }
        }

        /// <summary>
        /// Check if discovery is running
        /// </summary>
        public bool IsRunning => _isRunning;
    }

    // Supporting classes and data structures
    public class NetworkDiscoveryConfig
    {
        public int BroadcastPort { get; set; }
        public int DiscoveryPort { get; set; }
        public int DiscoveryInterval { get; set; }
        public int DiscoveryTimeout { get; set; }
        public int MaxRetries { get; set; }
    }

    public class DiscoveredServer
    {
        public string ServiceName { get; set; }
        public string ServerIP { get; set; }
        public int ServerPort { get; set; }
        public string Version { get; set; }
        public List<string> Capabilities { get; set; } = new List<string>();
        public string DeploymentMode { get; set; }
        public string DiscoveryMethod { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
    }

    public class ServerDiscoveredEventArgs : EventArgs
    {
        public DiscoveredServer Server { get; }
        
        public ServerDiscoveredEventArgs(DiscoveredServer server)
        {
            Server = server;
        }
    }

    // JSON message classes
    public class ServerAnnouncementMessage
    {
        public string type { get; set; }
        public ServerInfo server { get; set; }
    }

    public class DiscoveryResponseMessage
    {
        public string type { get; set; }
        public ServerInfo server { get; set; }
        public long respondedAt { get; set; }
    }

    public class ServerInfo
    {
        public string serviceName { get; set; }
        public string serverIP { get; set; }
        public int serverPort { get; set; }
        public string version { get; set; }
        public long timestamp { get; set; }
        public string[] capabilities { get; set; }
        public string deploymentMode { get; set; }
    }
}
