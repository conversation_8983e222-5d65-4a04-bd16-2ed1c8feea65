import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { authApi, usersApi } from '@/lib/api';
import { connectSocket, disconnectSocket, preWarmConnection } from '@/lib/socket';
import { clearAllManagedIntervals } from '@/lib/interval-manager';

export interface AuthSlice {
  currentUser: AppState['currentUser'];
  login: AppState['login'];
  logout: AppState['logout'];
  registerUser: AppState['registerUser'];
  pendingRegistrations: AppState['pendingRegistrations'];
  approvePendingUser: AppState['approvePendingUser'];
  rejectPendingUser: AppState['rejectPendingUser'];
  setMockAdminUser: AppState['setMockAdminUser'];
  fetchCurrentUser: () => Promise<boolean>;
  fetchPendingRegistrations: () => Promise<boolean>;
  passwordChangeRequests: AppState['passwordChangeRequests'];
  fetchPasswordChangeRequests: () => Promise<boolean>;
  approvePasswordChangeRequest: (requestId: string) => Promise<boolean>;
  rejectPasswordChangeRequest: (requestId: string, reason?: string) => Promise<boolean>;
}

export const createAuthSlice: StateCreator<AppState, [], [], AuthSlice> = (set, get) => ({
  currentUser: null,
  pendingRegistrations: [],
  passwordChangeRequests: [],

  // Simplified login for LAN deployment
  login: async (department, username, password, isGuest = false) => {
    try {
      console.log('🔐 VMS Login attempt:', { department, username });

      const response = await authApi.login({
        department: department.trim(),
        username: username.trim(),
        password: password
      });

      if (response.success && response.user) {
        const user = response.user;

        // BROWSER CLOSE FIX: Store session ID for immediate logout functionality
        // Session cookie is managed by browser, but we need sessionId for browser close detection

        // Store user in browser session with sessionId
        set({
          currentUser: {
            id: user.id,
            name: user.name,
            department: user.department,
            role: user.role,
            lastLogin: user.lastLogin,
            loginTime: Date.now(),
            sessionId: response.sessionId // Store sessionId for browser close detection
          }
        });

        console.log('✅ Login successful:', user.name, '(' + user.department + ')');
        console.log('🔐 Session managed via cookies');

        // CRITICAL FIX: Fetch vouchers immediately after login
        console.log('🔄 Fetching vouchers immediately after login for:', user.department);
        try {
          const { fetchVouchers } = get();
          await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers so departments can see vouchers sent to audit
          console.log('✅ Vouchers fetched successfully after login');
        } catch (voucherError) {
          console.error('❌ Failed to fetch vouchers after login:', voucherError);
          // Don't fail login if voucher fetch fails
        }

        // CRITICAL FIX: Force WebSocket disconnection and reconnection to use new session
        console.log('🔧 SESSION FIX: Forcing WebSocket reconnection with new session');
        disconnectSocket();

        // Wait a moment for disconnection to complete
        await new Promise(resolve => setTimeout(resolve, 100));

        // PRODUCTION-LEVEL: Parallel connection setup with pre-warming
        console.log('🚀 OPTIMIZED LOGIN: Starting parallel connection setup for:', user.name, '(' + user.department + ')');

        // Start pre-warming and connection in parallel
        const connectionPromises = [
          preWarmConnection().catch(() => {
            console.log('🚀 Pre-warming failed, falling back to regular connection');
            return connectSocket();
          })
        ];

        // Execute connection setup without blocking login completion
        Promise.all(connectionPromises).then(() => {
          console.log('🚀 OPTIMIZED LOGIN: Connection setup completed');
        }).catch(error => {
          console.error('🚀 OPTIMIZED LOGIN: Connection setup failed:', error);
          // Fallback to regular connection
          connectSocket();
        });

        return true;
      } else {
        console.error('❌ Login failed - Invalid response');
        return false;
      }

    } catch (error) {
      console.error('❌ Login error:', error);
      return false;
    }
  },
  // Simplified logout for LAN deployment
  logout: async () => {
    try {
      const currentUser = get().currentUser;
      console.log('🚪 Logging out user:', currentUser?.name);

      // CRITICAL FIX: Clear all managed intervals to stop periodic API calls
      clearAllManagedIntervals();

      // Disconnect from WebSocket
      disconnectSocket();
      console.log('🔌 WebSocket disconnected');

      // FIXED: Clear user session (cookies cleared by server)
      set({ currentUser: null });
      console.log('✅ User session cleared');

      // Optional: Call server logout for logging purposes
      await authApi.logout();

      return true;
    } catch (error) {
      console.error('Logout error:', error);

      // Even if API call fails, clear local state
      // Clear all managed intervals
      clearAllManagedIntervals();

      disconnectSocket();
      set({ currentUser: null });
      console.log('Forced logout due to error');

      return true;
    }
  },

  registerUser: async (name, password, department) => {
    try {
      // Call register API
      await authApi.register(name, password, department);

      // For local state, create a pending registration
      const newRegistration = {
        id: `reg-${Date.now()}`,
        name: name.toUpperCase(),
        password,
        department: department.toUpperCase(),
        dateRequested: new Date().toISOString(),
        status: 'pending'
      };

      set(state => ({
        pendingRegistrations: [...state.pendingRegistrations, newRegistration]
      }));

      return true;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    }
  },

  approvePendingUser: async (registrationId) => {
    try {
      // Call approve registration API
      await usersApi.approveRegistration(registrationId);

      // Update local state
      const registration = get().pendingRegistrations.find(r => r.id === registrationId);

      if (registration) {
        // Create a new user from the registration for local state
        const newUser = {
          id: `user-${Date.now()}`,
          name: registration.name.toUpperCase(),
          password: registration.password,
          department: registration.department.toUpperCase(),
          role: 'VIEWER',
          dateCreated: new Date().toISOString(),
          isActive: true
        };

        // Add the user to the users list
        get().addUser(newUser);
      }

      // Remove from pending registrations
      set(state => ({
        pendingRegistrations: state.pendingRegistrations.filter(r => r.id !== registrationId)
      }));

      return true;
    } catch (error) {
      console.error('Approve registration error:', error);
      return false;
    }
  },

  rejectPendingUser: async (registrationId) => {
    try {
      // Call reject registration API
      await usersApi.rejectRegistration(registrationId);

      // Update local state
      set(state => ({
        pendingRegistrations: state.pendingRegistrations.filter(r => r.id !== registrationId)
      }));

      return true;
    } catch (error) {
      console.error('Reject registration error:', error);
      return false;
    }
  },

  setMockAdminUser: (user) => {
    set({ currentUser: user });
  },

  // Fetch current user from API
  fetchCurrentUser: async () => {
    try {
      // Call get current user API
      const user = await authApi.getCurrentUser();

      // Set current user in state
      set({ currentUser: user });

      // PRODUCTION-LEVEL: Optimized connection for session restore
      console.log('🚀 SESSION RESTORE: Starting optimized connection for:', user.name, '(' + user.department + ')');

      // Use pre-warming for all users during session restore
      preWarmConnection().then(() => {
        console.log('🚀 SESSION RESTORE: Pre-warmed connection ready');
      }).catch(() => {
        console.log('🚀 SESSION RESTORE: Pre-warming failed, using regular connection');
        connectSocket();
      });

      return true;
    } catch (error: any) {
      // PRODUCTION FIX: Complete session cleanup on authentication failure
      console.log('🧹 CLEANUP: Authentication failed, performing complete session cleanup');

      // Clear current user in state
      set({ currentUser: null });

      // Clear all session-related localStorage data
      const keysToRemove = [
        'auth_error',
        'session_restore_failed',
        'vms_session_data',
        'user_session'
      ];

      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (e) {
          console.warn('Failed to remove localStorage key:', key);
        }
      });

      // Clear any persisted store data that might contain session info
      try {
        const storeData = localStorage.getItem('voucher-management-system');
        if (storeData) {
          const parsed = JSON.parse(storeData);
          if (parsed.state && parsed.state.currentUser) {
            parsed.state.currentUser = null;
            localStorage.setItem('voucher-management-system', JSON.stringify(parsed));
            console.log('🧹 CLEANUP: Cleared persisted currentUser from store');
          }
        }
      } catch (e) {
        console.warn('Failed to clean persisted store data:', e);
      }

      // WEBSOCKET FIX: Handle session invalidation gracefully
      if (error.status === 401 || error.type === 'auth') {
        console.warn('🔐 Session invalid during fetchCurrentUser - state cleared');
        // Error handling and redirect is handled by api.ts interceptor
        return false;
      } else {
        console.error('Failed to fetch current user:', error);
        return false;
      }
    }
  },

  // Directly set pending registrations in state
  setPendingRegistrations: (registrations) => {
    set({ pendingRegistrations: registrations });
    console.log('Set pending registrations:', registrations.length);
  },

  // Fetch pending registrations from API
  fetchPendingRegistrations: async () => {
    try {
      // Call get pending registrations API with timestamp to prevent caching
      const registrations = await usersApi.getPendingRegistrations();

      // Set pending registrations in state
      set({ pendingRegistrations: registrations });

      console.log('Fetched pending registrations:', registrations.length);

      return true;
    } catch (error) {
      console.error('Fetch pending registrations error:', error);
      return false;
    }
  },

  // Fetch password change requests from API
  fetchPasswordChangeRequests: async () => {
    try {
      const requests = await usersApi.getPasswordChangeRequests();
      // Ensure requests is always an array
      const safeRequests = Array.isArray(requests) ? requests : [];
      set({ passwordChangeRequests: safeRequests });
      console.log('Fetched password change requests:', safeRequests.length);
      return true;
    } catch (error) {
      console.error('Fetch password change requests error:', error);
      // Set empty array on error to prevent filter issues
      set({ passwordChangeRequests: [] });
      return false;
    }
  },

  // Approve password change request
  approvePasswordChangeRequest: async (requestId: string) => {
    try {
      const currentUser = get().currentUser;
      if (!currentUser) {
        throw new Error('Admin authentication required');
      }

      await usersApi.approvePasswordChangeRequest(requestId);

      // Remove from password change requests
      set(state => ({
        passwordChangeRequests: state.passwordChangeRequests.filter(r => r.id !== requestId)
      }));

      return true;
    } catch (error) {
      console.error('Approve password change request error:', error);
      return false;
    }
  },

  // Reject password change request
  rejectPasswordChangeRequest: async (requestId: string, reason?: string) => {
    try {
      const currentUser = get().currentUser;
      if (!currentUser) {
        throw new Error('Admin authentication required');
      }

      await usersApi.rejectPasswordChangeRequest(requestId, reason);

      // Remove from password change requests
      set(state => ({
        passwordChangeRequests: state.passwordChangeRequests.filter(r => r.id !== requestId)
      }));

      return true;
    } catch (error) {
      console.error('Reject password change request error:', error);
      return false;
    }
  },
});
