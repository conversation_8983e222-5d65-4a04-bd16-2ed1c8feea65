<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS-ADMIN Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #F5F5F5;
            color: #333;
        }

        .header {
            background: #1565C0;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
            font-weight: bold;
        }

        .header .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #1565C0;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background: #4CAF50; }
        .status-degraded { background: #FF9800; }
        .status-critical { background: #F44336; }
        .status-offline { background: #9E9E9E; }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #F0F0F0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #666;
            font-size: 14px;
        }

        .metric-value {
            font-weight: bold;
            color: #333;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #1565C0;
            color: white;
        }

        .btn-primary:hover {
            background: #1976D2;
        }

        .btn-secondary {
            background: #757575;
            color: white;
        }

        .btn-secondary:hover {
            background: #9E9E9E;
        }

        .btn-danger {
            background: #F44336;
            color: white;
        }

        .btn-danger:hover {
            background: #D32F2F;
        }

        .btn:disabled {
            background: #BDBDBD;
            cursor: not-allowed;
        }

        .network-mode {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .mode-static {
            background: #E8F5E8;
            color: #2E7D32;
        }

        .mode-dynamic {
            background: #FFF3E0;
            color: #F57C00;
        }

        .mode-hybrid {
            background: #E3F2FD;
            color: #1565C0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading-spinner {
            border: 3px solid #E0E0E0;
            border-top: 3px solid #1565C0;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #4CAF50;
        }

        .alert-error {
            background: #FFEBEE;
            color: #C62828;
            border: 1px solid #F44336;
        }

        .alert-warning {
            background: #FFF8E1;
            color: #F57C00;
            border: 1px solid #FF9800;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎛️ VMS-ADMIN Dashboard</h1>
        <div class="user-info">
            <span id="userInfo">Loading...</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <div class="container">
        <div id="alerts"></div>
        
        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <p>Loading dashboard data...</p>
        </div>

        <div class="dashboard-grid" id="dashboardContent" style="display: none;">
            <!-- System Overview Card -->
            <div class="card">
                <h3>🖥️ System Overview</h3>
                <div class="metric">
                    <span class="metric-label">Overall Status</span>
                    <span class="metric-value" id="systemStatus">
                        <span class="status-indicator status-healthy"></span>Loading...
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">VMS Server</span>
                    <span class="metric-value" id="vmsServerStatus">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Uptime</span>
                    <span class="metric-value" id="systemUptime">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Connections</span>
                    <span class="metric-value" id="activeConnections">Loading...</span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="refreshDashboard()">🔄 Refresh</button>
                    <button class="btn btn-danger" onclick="restartVMS()">🔄 Restart VMS</button>
                </div>
            </div>

            <!-- Network Management Card -->
            <div class="card">
                <h3>🌐 Network Management</h3>
                <div class="metric">
                    <span class="metric-label">Network Mode</span>
                    <span class="metric-value" id="networkMode">
                        <span class="network-mode mode-static">Loading...</span>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Static IP</span>
                    <span class="metric-value" id="staticIP">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Dynamic IP</span>
                    <span class="metric-value" id="dynamicIP">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Connectivity</span>
                    <span class="metric-value" id="networkConnectivity">Loading...</span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="switchToStatic()">📍 Switch to Static</button>
                    <button class="btn btn-secondary" onclick="switchToDynamic()">🔄 Switch to Dynamic</button>
                    <button class="btn btn-secondary" onclick="triggerHealing()">🏥 Trigger Healing</button>
                </div>
            </div>

            <!-- Database Management Card -->
            <div class="card">
                <h3>🗄️ Database Management</h3>
                <div class="metric">
                    <span class="metric-label">Database Status</span>
                    <span class="metric-value" id="databaseStatus">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tables</span>
                    <span class="metric-value" id="databaseSize">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Backup</span>
                    <span class="metric-value" id="lastBackup">Loading...</span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="createBackup()">💾 Create Backup</button>
                    <button class="btn btn-secondary" onclick="viewBackups()">📋 View Backups</button>
                </div>
            </div>

            <!-- User Management Card -->
            <div class="card">
                <h3>👥 User Management</h3>
                <div class="metric">
                    <span class="metric-label">Total Active Users</span>
                    <span class="metric-value" id="totalUsers">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Admin Sessions</span>
                    <span class="metric-value" id="adminSessions">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Department Breakdown</span>
                    <span class="metric-value" id="departmentBreakdown">Loading...</span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="viewUsers()">👥 View Users</button>
                    <button class="btn btn-secondary" onclick="viewSessions()">📊 View Sessions</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let socket;
        let currentUser = null;

        // Initialize dashboard
        async function initDashboard() {
            try {
                // Check authentication
                const authResponse = await fetch('/api/auth/status');
                const authData = await authResponse.json();
                
                if (!authData.authenticated) {
                    window.location.href = '/login.html';
                    return;
                }
                
                currentUser = authData.user;
                document.getElementById('userInfo').textContent = 
                    `${currentUser.username} (${currentUser.role})`;
                
                // Initialize Socket.IO
                socket = io();
                
                socket.on('connect', () => {
                    console.log('Connected to VMS-ADMIN service');
                    socket.emit('authenticate', { sessionId: 'current-session' });
                });
                
                socket.on('authenticated', (data) => {
                    if (data.success) {
                        console.log('Socket authenticated successfully');
                        loadDashboardData();
                    } else {
                        showAlert('Authentication failed', 'error');
                    }
                });
                
                socket.on('dashboard-stats', (stats) => {
                    updateDashboard(stats);
                });
                
                socket.on('network-change', (event) => {
                    showAlert(`Network change: ${event.type}`, 'warning');
                    refreshDashboard();
                });
                
                socket.on('fallback-decision', (decision) => {
                    showAlert(`Fallback decision: ${decision.action}`, 'warning');
                });
                
                socket.on('healing-action', (action) => {
                    const type = action.success ? 'success' : 'error';
                    showAlert(`Healing action: ${action.description} - ${action.success ? 'Success' : 'Failed'}`, type);
                });
                
            } catch (error) {
                console.error('Dashboard initialization error:', error);
                showAlert('Failed to initialize dashboard', 'error');
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const stats = await response.json();
                updateDashboard(stats);
                
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('dashboardContent').style.display = 'grid';
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showAlert('Failed to load dashboard data', 'error');
            }
        }

        // Update dashboard with new data
        function updateDashboard(stats) {
            // System Overview
            const statusClass = `status-${stats.systemStatus}`;
            document.getElementById('systemStatus').innerHTML = 
                `<span class="status-indicator ${statusClass}"></span>${stats.systemStatus}`;
            
            document.getElementById('vmsServerStatus').textContent = stats.vmsServer.status;
            document.getElementById('systemUptime').textContent = formatUptime(stats.vmsServer.uptime);
            document.getElementById('activeConnections').textContent = stats.vmsServer.connections;
            
            // Network Management
            const modeClass = `mode-${stats.networkSystem.mode}`;
            document.getElementById('networkMode').innerHTML = 
                `<span class="network-mode ${modeClass}">${stats.networkSystem.mode}</span>`;
            
            document.getElementById('staticIP').textContent = stats.networkSystem.staticIP || 'Not assigned';
            document.getElementById('dynamicIP').textContent = stats.networkSystem.dynamicIP || 'Not available';
            document.getElementById('networkConnectivity').textContent = 
                stats.networkSystem.connectivity ? 'Connected' : 'Disconnected';
            
            // Database Management
            document.getElementById('databaseStatus').textContent = stats.database.status;
            document.getElementById('databaseSize').textContent = `${stats.database.size} tables`;
            document.getElementById('lastBackup').textContent = 
                stats.database.lastBackup ? new Date(stats.database.lastBackup).toLocaleString() : 'Never';
            
            // User Management
            document.getElementById('totalUsers').textContent = stats.users.totalActive;
            document.getElementById('adminSessions').textContent = stats.users.adminSessions;
            
            const deptBreakdown = Object.entries(stats.users.departmentBreakdown)
                .map(([dept, count]) => `${dept}: ${count}`)
                .join(', ') || 'No data';
            document.getElementById('departmentBreakdown').textContent = deptBreakdown;
        }

        // Utility functions
        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return `${days}d ${hours % 24}h`;
            if (hours > 0) return `${hours}h ${minutes % 60}m`;
            if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
            return `${seconds}s`;
        }

        function showAlert(message, type = 'info') {
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            alertsContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Action functions
        async function refreshDashboard() {
            socket.emit('request-stats');
        }

        async function restartVMS() {
            if (confirm('Are you sure you want to restart the VMS server?')) {
                try {
                    const response = await fetch('/api/system/restart-vms', { method: 'POST' });
                    const result = await response.json();
                    showAlert(result.message, result.success ? 'success' : 'error');
                } catch (error) {
                    showAlert('Failed to restart VMS server', 'error');
                }
            }
        }

        async function switchToStatic() {
            try {
                const response = await fetch('/api/system/switch-network-mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ mode: 'static' })
                });
                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showAlert('Failed to switch to static mode', 'error');
            }
        }

        async function switchToDynamic() {
            try {
                const response = await fetch('/api/system/switch-network-mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ mode: 'dynamic' })
                });
                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showAlert('Failed to switch to dynamic mode', 'error');
            }
        }

        async function triggerHealing() {
            try {
                const response = await fetch('/api/system/trigger-healing', { method: 'POST' });
                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showAlert('Failed to trigger healing', 'error');
            }
        }

        async function createBackup() {
            try {
                const response = await fetch('/api/database/backup', { method: 'POST' });
                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showAlert('Failed to create backup', 'error');
            }
        }

        function viewBackups() {
            showAlert('Backup management feature coming soon', 'info');
        }

        function viewUsers() {
            showAlert('User management feature coming soon', 'info');
        }

        function viewSessions() {
            showAlert('Session management feature coming soon', 'info');
        }

        async function logout() {
            try {
                await fetch('/api/auth/logout', { method: 'POST' });
                window.location.href = '/login.html';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/login.html';
            }
        }

        // Initialize dashboard on page load
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
