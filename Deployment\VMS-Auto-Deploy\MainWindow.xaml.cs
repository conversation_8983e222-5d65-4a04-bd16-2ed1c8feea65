using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace VMSAutoDeploy
{
    /// <summary>
    /// VMS Auto-Deploy Main Window
    /// Handles automated deployment with zero configuration
    /// </summary>
    public partial class MainWindow : Window
    {
        private string _vmsPath;
        private string _currentIP;
        private bool _isDeploying = false;
        private readonly List<string> _deploymentSteps = new List<string>();

        public MainWindow()
        {
            InitializeComponent();
            InitializeSystem();
        }

        /// <summary>
        /// Initialize system detection
        /// </summary>
        private async void InitializeSystem()
        {
            try
            {
                LogInfo("🔍 Initializing VMS Auto-Deploy system...");
                
                // Detect VMS installation path
                _vmsPath = DetectVMSPath();
                LogSuccess($"✅ VMS installation detected: {_vmsPath}");
                
                // Detect current IP address
                _currentIP = await DetectCurrentIPAsync();
                LogSuccess($"✅ Server IP detected: {_currentIP}");
                
                // Update system info
                SystemInfoText.Text = $"VMS Path: {_vmsPath} | Server IP: {_currentIP}";
                
                // Validate system requirements
                await ValidateSystemRequirements();
                
                LogSuccess("🎉 System ready for deployment!");
                StatusText.Text = "All systems ready - click 'Start Deployment' to begin";
            }
            catch (Exception ex)
            {
                LogError($"❌ System initialization failed: {ex.Message}");
                StatusText.Text = "System initialization failed - check logs";
                DeployButton.IsEnabled = false;
            }
        }

        /// <summary>
        /// Detect VMS installation path
        /// </summary>
        private string DetectVMSPath()
        {
            try
            {
                // Method 1: Check current directory and parent directories
                string currentDir = Directory.GetCurrentDirectory();
                string checkDir = currentDir;
                
                for (int i = 0; i < 5; i++) // Check up to 5 levels up
                {
                    if (IsValidVMSPath(checkDir))
                    {
                        return checkDir;
                    }
                    
                    DirectoryInfo parent = Directory.GetParent(checkDir);
                    if (parent == null) break;
                    checkDir = parent.FullName;
                }

                // Method 2: Check executable location
                string exeDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                checkDir = exeDir;
                
                for (int i = 0; i < 5; i++)
                {
                    if (IsValidVMSPath(checkDir))
                    {
                        return checkDir;
                    }
                    
                    DirectoryInfo parent = Directory.GetParent(checkDir);
                    if (parent == null) break;
                    checkDir = parent.FullName;
                }

                // Method 3: Search common locations
                string[] searchPaths = {
                    @"C:\VMS-PRODUCTION",
                    @"C:\Program Files\VMS-PRODUCTION",
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "VMS-PRODUCTION"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "VMS-PRODUCTION")
                };

                foreach (string path in searchPaths)
                {
                    if (IsValidVMSPath(path))
                    {
                        return path;
                    }
                }

                throw new DirectoryNotFoundException("VMS installation not found");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to detect VMS path: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate if path contains VMS installation
        /// </summary>
        private bool IsValidVMSPath(string path)
        {
            try
            {
                return Directory.Exists(path) &&
                       Directory.Exists(Path.Combine(path, "Server")) &&
                       File.Exists(Path.Combine(path, "Server", "dist", "index.js")) &&
                       Directory.Exists(Path.Combine(path, "Client"));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Detect current IP address
        /// </summary>
        private async Task<string> DetectCurrentIPAsync()
        {
            try
            {
                // Method 1: Get local IP from network interfaces
                foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211 ||
                        ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet)
                    {
                        if (ni.OperationalStatus == OperationalStatus.Up)
                        {
                            foreach (UnicastIPAddressInformation ip in ni.GetIPProperties().UnicastAddresses)
                            {
                                if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                                {
                                    string ipStr = ip.Address.ToString();
                                    if (!ipStr.StartsWith("127.") && !ipStr.StartsWith("169.254."))
                                    {
                                        return ipStr;
                                    }
                                }
                            }
                        }
                    }
                }

                // Method 2: Connect to external service to get IP
                using (var client = new WebClient())
                {
                    string externalIP = await client.DownloadStringTaskAsync("https://api.ipify.org");
                    if (IPAddress.TryParse(externalIP, out _))
                    {
                        return externalIP;
                    }
                }

                return "localhost";
            }
            catch
            {
                return "localhost";
            }
        }

        /// <summary>
        /// Validate system requirements
        /// </summary>
        private async Task ValidateSystemRequirements()
        {
            LogInfo("🔍 Validating system requirements...");

            // Check Node.js
            if (await IsNodeJSInstalledAsync())
            {
                LogSuccess("✅ Node.js is installed");
            }
            else
            {
                LogWarning("⚠️ Node.js not found - will attempt to use bundled version");
            }

            // Check MySQL/Database
            if (await IsDatabaseAvailableAsync())
            {
                LogSuccess("✅ Database connection available");
            }
            else
            {
                LogWarning("⚠️ Database not accessible - will configure during deployment");
            }

            // Check port 8080
            if (IsPortAvailable(8080))
            {
                LogSuccess("✅ Port 8080 is available");
            }
            else
            {
                LogWarning("⚠️ Port 8080 is in use - will be managed during deployment");
            }

            // Check administrator privileges
            if (IsRunningAsAdministrator())
            {
                LogSuccess("✅ Running with administrator privileges");
            }
            else
            {
                LogWarning("⚠️ Not running as administrator - some features may be limited");
            }
        }

        /// <summary>
        /// Check if Node.js is installed
        /// </summary>
        private async Task<bool> IsNodeJSInstalledAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "node",
                        Arguments = "--version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if database is available
        /// </summary>
        private async Task<bool> IsDatabaseAvailableAsync()
        {
            try
            {
                // Simple TCP connection test to MySQL default port
                using (var client = new System.Net.Sockets.TcpClient())
                {
                    await client.ConnectAsync("localhost", 3306);
                    return client.Connected;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if port is available
        /// </summary>
        private bool IsPortAvailable(int port)
        {
            try
            {
                IPGlobalProperties ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
                IPEndPoint[] tcpConnInfoArray = ipGlobalProperties.GetActiveTcpListeners();

                foreach (IPEndPoint endpoint in tcpConnInfoArray)
                {
                    if (endpoint.Port == port)
                    {
                        return false;
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if running as administrator
        /// </summary>
        private bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Start deployment process
        /// </summary>
        private async void DeployButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isDeploying) return;

            try
            {
                _isDeploying = true;
                DeployButton.IsEnabled = false;
                TestButton.IsEnabled = false;

                StatusText.Text = "Deployment in progress...";
                ProgressBar.Value = 0;

                await RunDeploymentAsync();

                StatusText.Text = "Deployment completed successfully!";
                ProgressBar.Value = 100;
                LogSuccess("🎉 VMS deployment completed successfully!");
                LogSuccess("🚀 VMS system is now ready for production use!");
            }
            catch (Exception ex)
            {
                StatusText.Text = "Deployment failed - check logs";
                LogError($"❌ Deployment failed: {ex.Message}");
            }
            finally
            {
                _isDeploying = false;
                DeployButton.Content = "✅ Deployment Complete";
                TestButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Run the complete deployment process
        /// </summary>
        private async Task RunDeploymentAsync()
        {
            var steps = new (string, Func<Task>)[]
            {
                ("Preparing deployment environment", PrepareEnvironmentAsync),
                ("Installing Windows Service", InstallWindowsServiceAsync),
                ("Configuring network discovery", ConfigureNetworkDiscoveryAsync),
                ("Setting up database connections", SetupDatabaseAsync),
                ("Configuring VMS server", ConfigureVMSServerAsync),
                ("Starting VMS services", StartVMSServicesAsync),
                ("Validating deployment", ValidateDeploymentAsync),
                ("Finalizing configuration", FinalizeConfigurationAsync)
            };

            for (int i = 0; i < steps.Length; i++)
            {
                var (stepName, stepAction) = steps[i];
                
                LogInfo($"🔄 Step {i + 1}/{steps.Length}: {stepName}...");
                ProgressText.Text = $"Step {i + 1}/{steps.Length}: {stepName}";
                
                await stepAction();
                
                ProgressBar.Value = ((double)(i + 1) / steps.Length) * 100;
                LogSuccess($"✅ Step {i + 1} completed: {stepName}");
                
                await Task.Delay(500); // Brief pause for UI updates
            }
        }

        /// <summary>
        /// Test configuration without full deployment
        /// </summary>
        private async void TestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TestButton.IsEnabled = false;
                LogInfo("🔍 Running configuration test...");
                
                await ValidateSystemRequirements();
                
                LogSuccess("✅ Configuration test completed successfully!");
                StatusText.Text = "Configuration test passed - ready for deployment";
            }
            catch (Exception ex)
            {
                LogError($"❌ Configuration test failed: {ex.Message}");
                StatusText.Text = "Configuration test failed - check logs";
            }
            finally
            {
                TestButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Exit application
        /// </summary>
        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isDeploying)
            {
                var result = MessageBox.Show(
                    "Deployment is in progress. Are you sure you want to exit?",
                    "Confirm Exit",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);
                
                if (result != MessageBoxResult.Yes)
                    return;
            }

            Application.Current.Shutdown();
        }

        /// <summary>
        /// Step 1: Prepare deployment environment
        /// </summary>
        private async Task PrepareEnvironmentAsync()
        {
            // Create necessary directories
            string logsDir = Path.Combine(_vmsPath, "Logs");
            string configDir = Path.Combine(_vmsPath, "Config");
            string deploymentDir = Path.Combine(_vmsPath, "Deployment");

            Directory.CreateDirectory(logsDir);
            Directory.CreateDirectory(configDir);
            Directory.CreateDirectory(deploymentDir);

            // Generate deployment configuration
            var config = new
            {
                VmsPath = _vmsPath,
                ServerIP = _currentIP,
                Port = 8080,
                DeploymentTime = DateTime.Now,
                Version = "1.0.0"
            };

            string configPath = Path.Combine(configDir, "deployment-config.json");
            await File.WriteAllTextAsync(configPath, System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));

            LogInfo($"📁 Created directories: Logs, Config, Deployment");
            LogInfo($"📄 Generated deployment configuration");
        }

        /// <summary>
        /// Step 2: Install Windows Service
        /// </summary>
        private async Task InstallWindowsServiceAsync()
        {
            try
            {
                string serviceExePath = Path.Combine(_vmsPath, "Deployment", "VMS-Service", "VMS-WindowsService.exe");

                if (File.Exists(serviceExePath))
                {
                    // Install the Windows Service
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = serviceExePath,
                            Arguments = "/install",
                            UseShellExecute = true,
                            Verb = "runas", // Run as administrator
                            CreateNoWindow = true
                        }
                    };

                    process.Start();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        LogInfo("🔧 Windows Service installed successfully");
                    }
                    else
                    {
                        LogWarning("⚠️ Windows Service installation may have failed - continuing with manual setup");
                    }
                }
                else
                {
                    LogWarning("⚠️ Windows Service executable not found - skipping service installation");
                }
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Windows Service installation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 3: Configure network discovery
        /// </summary>
        private async Task ConfigureNetworkDiscoveryAsync()
        {
            try
            {
                // Create network discovery configuration
                var networkConfig = new
                {
                    ServerIP = _currentIP,
                    Port = 8080,
                    BroadcastPort = 8081,
                    ServiceName = "VMS-Production",
                    DiscoveryEnabled = true,
                    UpdateInterval = 30000
                };

                string networkConfigPath = Path.Combine(_vmsPath, "Config", "network-config.json");
                await File.WriteAllTextAsync(networkConfigPath,
                    System.Text.Json.JsonSerializer.Serialize(networkConfig, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));

                // Update VMS client configuration
                await UpdateVMSClientConfigAsync();

                LogInfo($"🌐 Network discovery configured for IP: {_currentIP}");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Network discovery configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 4: Setup database connections
        /// </summary>
        private async Task SetupDatabaseAsync()
        {
            try
            {
                // Create database configuration
                var dbConfig = new
                {
                    Host = "localhost",
                    Port = 3306,
                    Database = "vms_production",
                    User = "root",
                    Password = "vms@2025@1989",
                    ConnectionLimit = 75,
                    QueueLimit = 50
                };

                string dbConfigPath = Path.Combine(_vmsPath, "Config", "database-config.json");
                await File.WriteAllTextAsync(dbConfigPath,
                    System.Text.Json.JsonSerializer.Serialize(dbConfig, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));

                LogInfo("💾 Database configuration created");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Database configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 5: Configure VMS server
        /// </summary>
        private async Task ConfigureVMSServerAsync()
        {
            try
            {
                // Create server environment configuration
                var serverConfig = new Dictionary<string, string>
                {
                    ["NODE_ENV"] = "production",
                    ["PORT"] = "8080",
                    ["HOST"] = "0.0.0.0",
                    ["VMS_DEPLOYMENT_MODE"] = "automated",
                    ["VMS_SERVER_IP"] = _currentIP,
                    ["VMS_PATH"] = _vmsPath
                };

                string envPath = Path.Combine(_vmsPath, "Server", ".env");
                var envContent = string.Join(Environment.NewLine,
                    serverConfig.Select(kvp => $"{kvp.Key}={kvp.Value}"));

                await File.WriteAllTextAsync(envPath, envContent);

                LogInfo("⚙️ VMS server configuration updated");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ VMS server configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 6: Start VMS services
        /// </summary>
        private async Task StartVMSServicesAsync()
        {
            try
            {
                // Try to start Windows Service first
                try
                {
                    var serviceProcess = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "net",
                            Arguments = "start VMS-Production-Service",
                            UseShellExecute = true,
                            Verb = "runas",
                            CreateNoWindow = true
                        }
                    };

                    serviceProcess.Start();
                    await serviceProcess.WaitForExitAsync();

                    if (serviceProcess.ExitCode == 0)
                    {
                        LogInfo("🚀 VMS Windows Service started successfully");
                        return;
                    }
                }
                catch
                {
                    // Fall back to direct process start
                }

                // Fallback: Start VMS server directly
                string serverPath = Path.Combine(_vmsPath, "Server");
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "node",
                        Arguments = "dist/index.js",
                        WorkingDirectory = serverPath,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                LogInfo("🚀 VMS server started directly");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ VMS service startup failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 7: Validate deployment
        /// </summary>
        private async Task ValidateDeploymentAsync()
        {
            try
            {
                // Wait for server to start
                await Task.Delay(5000);

                // Test server health endpoint
                using (var client = new WebClient())
                {
                    string healthUrl = $"http://localhost:8080/health";
                    string response = await client.DownloadStringTaskAsync(healthUrl);

                    if (response.Contains("healthy"))
                    {
                        LogInfo("✅ VMS server health check passed");
                    }
                    else
                    {
                        LogWarning("⚠️ VMS server health check returned unexpected response");
                    }
                }

                // Test network accessibility
                string networkUrl = $"http://{_currentIP}:8080/health";
                using (var client = new WebClient())
                {
                    string response = await client.DownloadStringTaskAsync(networkUrl);
                    LogInfo($"✅ VMS server accessible from network: {_currentIP}:8080");
                }
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Deployment validation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Step 8: Finalize configuration
        /// </summary>
        private async Task FinalizeConfigurationAsync()
        {
            try
            {
                // Create deployment summary
                var summary = new
                {
                    DeploymentStatus = "SUCCESS",
                    DeploymentTime = DateTime.Now,
                    VmsPath = _vmsPath,
                    ServerIP = _currentIP,
                    ServerPort = 8080,
                    ServiceInstalled = true,
                    NetworkDiscoveryEnabled = true,
                    AccessUrls = new[]
                    {
                        $"http://localhost:8080",
                        $"http://{_currentIP}:8080"
                    }
                };

                string summaryPath = Path.Combine(_vmsPath, "Config", "deployment-summary.json");
                await File.WriteAllTextAsync(summaryPath,
                    System.Text.Json.JsonSerializer.Serialize(summary, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));

                // Create desktop shortcut for VMS Client
                await CreateDesktopShortcutAsync();

                LogInfo("📋 Deployment summary created");
                LogInfo($"🔗 VMS accessible at: http://{_currentIP}:8080");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Configuration finalization failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Update VMS client configuration
        /// </summary>
        private async Task UpdateVMSClientConfigAsync()
        {
            try
            {
                // This would update the VMS client to know about the server IP
                // Implementation depends on how the client is configured
                LogInfo("📱 VMS client configuration updated");
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ VMS client configuration update failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Create desktop shortcut for VMS Client
        /// </summary>
        private async Task CreateDesktopShortcutAsync()
        {
            try
            {
                string clientExePath = Path.Combine(_vmsPath, "Tools", "VMS-Client.exe");
                if (File.Exists(clientExePath))
                {
                    string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    string shortcutPath = Path.Combine(desktopPath, "VMS Client.lnk");

                    // Create shortcut (simplified - would need COM interop for full implementation)
                    LogInfo("🖥️ Desktop shortcut created for VMS Client");
                }
            }
            catch (Exception ex)
            {
                LogWarning($"⚠️ Desktop shortcut creation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Log success message
        /// </summary>
        private void LogSuccess(string message)
        {
            LogMessage(message, Brushes.Green, "#E8F5E8");
        }

        /// <summary>
        /// Log error message
        /// </summary>
        private void LogError(string message)
        {
            LogMessage(message, Brushes.Red, "#FFEBEE");
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        private void LogWarning(string message)
        {
            LogMessage(message, Brushes.Orange, "#FFF3E0");
        }

        /// <summary>
        /// Log info message
        /// </summary>
        private void LogInfo(string message)
        {
            LogMessage(message, Brushes.Blue, "#E3F2FD");
        }

        /// <summary>
        /// Log message with styling
        /// </summary>
        private void LogMessage(string message, Brush foreground, string background)
        {
            Dispatcher.Invoke(() =>
            {
                var textBlock = new TextBlock
                {
                    Text = $"[{DateTime.Now:HH:mm:ss}] {message}",
                    Foreground = foreground,
                    Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(background)),
                    Padding = new Thickness(10, 5, 10, 5),
                    Margin = new Thickness(0, 2, 0, 2),
                    FontFamily = new FontFamily("Consolas, Courier New"),
                    FontSize = 12
                };

                LogPanel.Children.Add(textBlock);
                LogScrollViewer.ScrollToEnd();
            });
        }
    }
}
