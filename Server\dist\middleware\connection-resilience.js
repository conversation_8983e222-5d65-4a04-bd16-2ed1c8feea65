"use strict";
// VMS Server-Side Connection Resilience
// Production-grade connection handling with graceful disconnection
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverConnectionManager = void 0;
exports.healthCheckMiddleware = healthCheckMiddleware;
const logger_1 = require("../utils/logger");
class ServerConnectionManager {
    disconnectionTimers = new Map();
    GRACE_PERIOD = 10000; // 10 seconds grace period
    HEALTH_CHECK_THROTTLE = 30000; // 30 seconds between health checks per client
    lastHealthCheck = new Map();
    // Handle graceful disconnection with reconnection grace period
    handleDisconnection(userId, userName, department, socketId, cleanupCallback) {
        const timerKey = `${userId}-${socketId}`;
        // Cancel any existing timer for this user/socket
        this.cancelDisconnectionTimer(timerKey);
        logger_1.logger.info(`🔄 RESILIENCE: Starting ${this.GRACE_PERIOD / 1000}s grace period for ${userName} (${userId})`);
        // Set up grace period timer
        const timer = setTimeout(async () => {
            logger_1.logger.info(`⏰ RESILIENCE: Grace period expired for ${userName} (${userId}) - proceeding with cleanup`);
            try {
                await cleanupCallback();
                this.disconnectionTimers.delete(timerKey);
            }
            catch (error) {
                logger_1.logger.error(`❌ RESILIENCE: Cleanup failed for ${userName}:`, error);
            }
        }, this.GRACE_PERIOD);
        // Store the timer
        this.disconnectionTimers.set(timerKey, {
            userId,
            userName,
            department,
            timer
        });
    }
    // Cancel disconnection timer (user reconnected)
    cancelDisconnectionTimer(timerKey) {
        const existingTimer = this.disconnectionTimers.get(timerKey);
        if (existingTimer) {
            clearTimeout(existingTimer.timer);
            this.disconnectionTimers.delete(timerKey);
            logger_1.logger.info(`✅ RESILIENCE: Cancelled disconnection timer for ${existingTimer.userName}`);
            return true;
        }
        return false;
    }
    // Handle user reconnection
    handleReconnection(userId, userName, socketId) {
        const timerKey = `${userId}-${socketId}`;
        const cancelled = this.cancelDisconnectionTimer(timerKey);
        if (cancelled) {
            logger_1.logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) reconnected during grace period`);
        }
        else {
            logger_1.logger.info(`🔄 RESILIENCE: User ${userName} (${userId}) connected (no pending disconnection)`);
        }
    }
    // Throttle health checks to prevent spam
    shouldAllowHealthCheck(clientId) {
        const now = Date.now();
        const lastCheck = this.lastHealthCheck.get(clientId) || 0;
        if (now - lastCheck >= this.HEALTH_CHECK_THROTTLE) {
            this.lastHealthCheck.set(clientId, now);
            return true;
        }
        return false;
    }
    // Clean up expired health check records
    cleanupHealthCheckRecords() {
        const now = Date.now();
        const expiredThreshold = this.HEALTH_CHECK_THROTTLE * 2; // 2x throttle time
        for (const [clientId, lastCheck] of this.lastHealthCheck.entries()) {
            if (now - lastCheck > expiredThreshold) {
                this.lastHealthCheck.delete(clientId);
            }
        }
    }
    // Get connection statistics
    getConnectionStats() {
        return {
            pendingDisconnections: this.disconnectionTimers.size,
            healthCheckClients: this.lastHealthCheck.size,
            gracePeriodMs: this.GRACE_PERIOD,
            healthCheckThrottleMs: this.HEALTH_CHECK_THROTTLE
        };
    }
    // Cleanup all timers (for server shutdown)
    cleanup() {
        logger_1.logger.info('🧹 RESILIENCE: Cleaning up all connection timers');
        for (const [timerKey, timerData] of this.disconnectionTimers.entries()) {
            clearTimeout(timerData.timer);
            logger_1.logger.info(`🧹 RESILIENCE: Cleared timer for ${timerData.userName}`);
        }
        this.disconnectionTimers.clear();
        this.lastHealthCheck.clear();
    }
}
// Export singleton instance
exports.serverConnectionManager = new ServerConnectionManager();
// Middleware to optimize health check endpoints
function healthCheckMiddleware(req, res, next) {
    const clientId = req.ip + '-' + (req.headers['user-agent'] || 'unknown');
    // Check if this is a health check request
    if (req.path === '/api/health' || req.path === '/health') {
        // Throttle health checks per client
        if (!exports.serverConnectionManager.shouldAllowHealthCheck(clientId)) {
            // Return cached response for throttled requests
            return res.status(200).json({
                status: 'healthy',
                cached: true,
                timestamp: new Date().toISOString()
            });
        }
    }
    next();
}
// Periodic cleanup task
setInterval(() => {
    exports.serverConnectionManager.cleanupHealthCheckRecords();
}, 60000); // Clean up every minute
//# sourceMappingURL=connection-resilience.js.map