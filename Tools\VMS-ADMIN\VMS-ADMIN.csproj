<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>VMSADMIN</RootNamespace>
    <AssemblyName>VMS-ADMIN</AssemblyName>
    <UseWPF>true</UseWPF>

    <AssemblyTitle>VMS-ADMIN - System Administration Client</AssemblyTitle>
    <AssemblyDescription>Desktop client for VMS system administration and monitoring</AssemblyDescription>
    <AssemblyCompany>VMS Production Team</AssemblyCompany>
    <AssemblyProduct>VMS-ADMIN</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2025 VMS Production Team</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>



</Project>
