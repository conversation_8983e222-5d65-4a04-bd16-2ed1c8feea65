<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>VMSADMIN</RootNamespace>
    <AssemblyName>VMS-ADMIN</AssemblyName>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>vms-admin.ico</ApplicationIcon>
    <AssemblyTitle>VMS-ADMIN - System Administration Client</AssemblyTitle>
    <AssemblyDescription>Desktop client for VMS system administration and monitoring</AssemblyDescription>
    <AssemblyCompany>VMS Production Team</AssemblyCompany>
    <AssemblyProduct>VMS-ADMIN</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2025 VMS Production Team</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="vms-admin.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="vms-admin-logo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
