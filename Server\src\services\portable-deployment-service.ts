/**
 * Portable Deployment Service
 * Handles dynamic path resolution and configuration generation for unknown deployment paths
 * Enables VMS to run from any location without hardcoded paths
 */

import path from 'path';
import fs from 'fs/promises';
import { existsSync } from 'fs';
import os from 'os';
import { logger } from '../utils/logger.js';

export interface DeploymentPaths {
  vmsRoot: string;
  serverRoot: string;
  clientRoot: string;
  toolsRoot: string;
  logsRoot: string;
  configRoot: string;
  databasePath: string;
  backupRoot: string;
}

export interface DeploymentConfig {
  deploymentId: string;
  deploymentTime: Date;
  deploymentMode: 'automated' | 'manual' | 'service';
  serverIP: string;
  serverPort: number;
  paths: DeploymentPaths;
  systemInfo: {
    platform: string;
    hostname: string;
    username: string;
    nodeVersion: string;
  };
}

export class PortableDeploymentService {
  private _deploymentConfig: DeploymentConfig | null = null;
  private _isInitialized = false;

  /**
   * Initialize portable deployment system
   */
  public async initialize(): Promise<DeploymentConfig> {
    try {
      logger.info('🔧 Initializing Portable Deployment Service...');

      // Detect deployment paths
      const paths = await this.detectDeploymentPaths();
      
      // Get system information
      const systemInfo = this.getSystemInfo();
      
      // Create deployment configuration
      this._deploymentConfig = {
        deploymentId: this.generateDeploymentId(),
        deploymentTime: new Date(),
        deploymentMode: this.detectDeploymentMode(),
        serverIP: await this.detectServerIP(),
        serverPort: parseInt(process.env.PORT || '8080'),
        paths,
        systemInfo
      };

      // Ensure all directories exist
      await this.ensureDirectoriesExist();

      // Generate configuration files
      await this.generateConfigurationFiles();

      // Update environment variables
      this.updateEnvironmentVariables();

      this._isInitialized = true;
      
      logger.info('✅ Portable Deployment Service initialized successfully');
      logger.info(`📁 VMS Root: ${this._deploymentConfig.paths.vmsRoot}`);
      logger.info(`🖥️ Server IP: ${this._deploymentConfig.serverIP}:${this._deploymentConfig.serverPort}`);
      logger.info(`🆔 Deployment ID: ${this._deploymentConfig.deploymentId}`);

      return this._deploymentConfig;

    } catch (error) {
      logger.error('❌ Failed to initialize Portable Deployment Service:', error);
      throw error;
    }
  }

  /**
   * Detect deployment paths dynamically
   */
  private async detectDeploymentPaths(): Promise<DeploymentPaths> {
    try {
      // Method 1: Use current working directory and traverse up
      let vmsRoot = await this.findVMSRoot(process.cwd());
      
      // Method 2: Use script location
      if (!vmsRoot) {
        const scriptDir = path.dirname(process.argv[1] || __dirname);
        vmsRoot = await this.findVMSRoot(scriptDir);
      }

      // Method 3: Use __dirname and traverse up
      if (!vmsRoot) {
        vmsRoot = await this.findVMSRoot(__dirname);
      }

      // Method 4: Search common locations
      if (!vmsRoot) {
        vmsRoot = await this.searchCommonLocations();
      }

      if (!vmsRoot) {
        throw new Error('VMS installation directory not found');
      }

      // Resolve all paths relative to VMS root
      const paths: DeploymentPaths = {
        vmsRoot: path.resolve(vmsRoot),
        serverRoot: path.resolve(vmsRoot, 'Server'),
        clientRoot: path.resolve(vmsRoot, 'Client'),
        toolsRoot: path.resolve(vmsRoot, 'Tools'),
        logsRoot: path.resolve(vmsRoot, 'Logs'),
        configRoot: path.resolve(vmsRoot, 'Config'),
        databasePath: path.resolve(vmsRoot, 'Database'),
        backupRoot: path.resolve(vmsRoot, 'Backups')
      };

      // Validate critical paths
      await this.validatePaths(paths);

      return paths;

    } catch (error) {
      logger.error('Error detecting deployment paths:', error);
      throw error;
    }
  }

  /**
   * Find VMS root directory by traversing up from a starting directory
   */
  private async findVMSRoot(startDir: string): Promise<string | null> {
    try {
      let currentDir = path.resolve(startDir);
      const maxLevels = 10; // Prevent infinite loops
      
      for (let i = 0; i < maxLevels; i++) {
        if (await this.isVMSRoot(currentDir)) {
          return currentDir;
        }
        
        const parentDir = path.dirname(currentDir);
        if (parentDir === currentDir) {
          // Reached filesystem root
          break;
        }
        currentDir = parentDir;
      }
      
      return null;
    } catch (error) {
      logger.debug(`Error finding VMS root from ${startDir}:`, error);
      return null;
    }
  }

  /**
   * Check if directory is VMS root
   */
  private async isVMSRoot(dir: string): Promise<boolean> {
    try {
      const requiredPaths = [
        path.join(dir, 'Server'),
        path.join(dir, 'Server', 'dist', 'index.js'),
        path.join(dir, 'Server', 'package.json'),
        path.join(dir, 'Client')
      ];

      for (const requiredPath of requiredPaths) {
        if (!existsSync(requiredPath)) {
          return false;
        }
      }

      // Additional validation: check package.json for VMS markers
      const packageJsonPath = path.join(dir, 'Server', 'package.json');
      try {
        const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
        return packageJson.name && packageJson.name.toLowerCase().includes('vms');
      } catch {
        return true; // If we can't read package.json, but other files exist, assume it's VMS
      }

    } catch {
      return false;
    }
  }

  /**
   * Search common VMS installation locations
   */
  private async searchCommonLocations(): Promise<string | null> {
    const commonPaths = [
      'C:\\VMS-PRODUCTION',
      'C:\\Program Files\\VMS-PRODUCTION',
      'C:\\Program Files (x86)\\VMS-PRODUCTION',
      path.join(os.homedir(), 'Desktop', 'VMS-PRODUCTION'),
      path.join(os.homedir(), 'Documents', 'VMS-PRODUCTION'),
      path.join(os.homedir(), 'VMS-PRODUCTION'),
      '/opt/VMS-PRODUCTION',
      '/usr/local/VMS-PRODUCTION',
      path.join(os.homedir(), 'VMS-PRODUCTION')
    ];

    for (const searchPath of commonPaths) {
      try {
        if (await this.isVMSRoot(searchPath)) {
          return searchPath;
        }
      } catch {
        // Continue searching
      }
    }

    // Search all drives on Windows
    if (os.platform() === 'win32') {
      try {
        const drives = await this.getWindowsDrives();
        for (const drive of drives) {
          const vmsPath = path.join(drive, 'VMS-PRODUCTION');
          if (await this.isVMSRoot(vmsPath)) {
            return vmsPath;
          }
        }
      } catch {
        // Continue
      }
    }

    return null;
  }

  /**
   * Get available Windows drives
   */
  private async getWindowsDrives(): Promise<string[]> {
    const drives: string[] = [];
    
    for (let i = 65; i <= 90; i++) { // A-Z
      const drive = String.fromCharCode(i) + ':';
      try {
        await fs.access(drive);
        drives.push(drive);
      } catch {
        // Drive not available
      }
    }
    
    return drives;
  }

  /**
   * Validate that critical paths exist
   */
  private async validatePaths(paths: DeploymentPaths): Promise<void> {
    const criticalPaths = [
      { path: paths.vmsRoot, name: 'VMS Root' },
      { path: paths.serverRoot, name: 'Server Root' },
      { path: path.join(paths.serverRoot, 'dist', 'index.js'), name: 'Server Entry Point' }
    ];

    for (const { path: checkPath, name } of criticalPaths) {
      if (!existsSync(checkPath)) {
        throw new Error(`Critical path missing: ${name} (${checkPath})`);
      }
    }

    logger.info('✅ All critical paths validated');
  }

  /**
   * Ensure all required directories exist
   */
  private async ensureDirectoriesExist(): Promise<void> {
    if (!this._deploymentConfig) {
      throw new Error('Deployment config not initialized');
    }

    const directories = [
      this._deploymentConfig.paths.logsRoot,
      this._deploymentConfig.paths.configRoot,
      this._deploymentConfig.paths.backupRoot,
      path.join(this._deploymentConfig.paths.configRoot, 'generated'),
      path.join(this._deploymentConfig.paths.logsRoot, 'server'),
      path.join(this._deploymentConfig.paths.logsRoot, 'deployment'),
      path.join(this._deploymentConfig.paths.backupRoot, 'automated'),
      path.join(this._deploymentConfig.paths.backupRoot, 'manual')
    ];

    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
        logger.debug(`📁 Ensured directory exists: ${dir}`);
      } catch (error) {
        logger.warn(`⚠️ Could not create directory ${dir}:`, error);
      }
    }

    logger.info('✅ All required directories ensured');
  }

  /**
   * Generate configuration files with dynamic paths
   */
  private async generateConfigurationFiles(): Promise<void> {
    if (!this._deploymentConfig) {
      throw new Error('Deployment config not initialized');
    }

    const configDir = path.join(this._deploymentConfig.paths.configRoot, 'generated');

    // Generate paths configuration
    const pathsConfig = {
      generated: new Date().toISOString(),
      deploymentId: this._deploymentConfig.deploymentId,
      paths: this._deploymentConfig.paths,
      relativePaths: this.generateRelativePaths()
    };

    await fs.writeFile(
      path.join(configDir, 'paths.json'),
      JSON.stringify(pathsConfig, null, 2)
    );

    // Generate environment configuration
    const envConfig = {
      NODE_ENV: process.env.NODE_ENV || 'production',
      PORT: this._deploymentConfig.serverPort.toString(),
      HOST: '0.0.0.0',
      VMS_ROOT: this._deploymentConfig.paths.vmsRoot,
      VMS_SERVER_ROOT: this._deploymentConfig.paths.serverRoot,
      VMS_LOGS_ROOT: this._deploymentConfig.paths.logsRoot,
      VMS_CONFIG_ROOT: this._deploymentConfig.paths.configRoot,
      VMS_BACKUP_ROOT: this._deploymentConfig.paths.backupRoot,
      VMS_DEPLOYMENT_ID: this._deploymentConfig.deploymentId,
      VMS_DEPLOYMENT_MODE: this._deploymentConfig.deploymentMode,
      VMS_SERVER_IP: this._deploymentConfig.serverIP,
      VMS_PORTABLE_MODE: 'true'
    };

    // Write .env file
    const envContent = Object.entries(envConfig)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');

    await fs.writeFile(
      path.join(this._deploymentConfig.paths.serverRoot, '.env'),
      envContent
    );

    // Generate deployment summary
    await fs.writeFile(
      path.join(configDir, 'deployment-config.json'),
      JSON.stringify(this._deploymentConfig, null, 2)
    );

    logger.info('✅ Configuration files generated');
  }

  /**
   * Generate relative paths for portability
   */
  private generateRelativePaths(): any {
    if (!this._deploymentConfig) {
      return {};
    }

    const vmsRoot = this._deploymentConfig.paths.vmsRoot;
    
    return {
      server: path.relative(vmsRoot, this._deploymentConfig.paths.serverRoot),
      client: path.relative(vmsRoot, this._deploymentConfig.paths.clientRoot),
      tools: path.relative(vmsRoot, this._deploymentConfig.paths.toolsRoot),
      logs: path.relative(vmsRoot, this._deploymentConfig.paths.logsRoot),
      config: path.relative(vmsRoot, this._deploymentConfig.paths.configRoot),
      backups: path.relative(vmsRoot, this._deploymentConfig.paths.backupRoot)
    };
  }

  /**
   * Update environment variables for current process
   */
  private updateEnvironmentVariables(): void {
    if (!this._deploymentConfig) {
      return;
    }

    const envVars = {
      VMS_ROOT: this._deploymentConfig.paths.vmsRoot,
      VMS_SERVER_ROOT: this._deploymentConfig.paths.serverRoot,
      VMS_LOGS_ROOT: this._deploymentConfig.paths.logsRoot,
      VMS_CONFIG_ROOT: this._deploymentConfig.paths.configRoot,
      VMS_BACKUP_ROOT: this._deploymentConfig.paths.backupRoot,
      VMS_DEPLOYMENT_ID: this._deploymentConfig.deploymentId,
      VMS_DEPLOYMENT_MODE: this._deploymentConfig.deploymentMode,
      VMS_SERVER_IP: this._deploymentConfig.serverIP,
      VMS_PORTABLE_MODE: 'true'
    };

    Object.entries(envVars).forEach(([key, value]) => {
      process.env[key] = value;
    });

    logger.info('✅ Environment variables updated');
  }

  /**
   * Detect deployment mode
   */
  private detectDeploymentMode(): 'automated' | 'manual' | 'service' {
    if (process.env.VMS_SERVICE_MODE === 'true') {
      return 'service';
    }
    
    if (process.env.VMS_DEPLOYMENT_MODE === 'automated') {
      return 'automated';
    }
    
    return 'manual';
  }

  /**
   * Detect server IP address
   */
  private async detectServerIP(): Promise<string> {
    try {
      const interfaces = os.networkInterfaces();
      
      // Prefer Ethernet, then WiFi
      const preferredTypes = ['Ethernet', 'Wi-Fi', 'WiFi', 'Wireless'];
      
      for (const type of preferredTypes) {
        for (const [name, addresses] of Object.entries(interfaces)) {
          if (name.toLowerCase().includes(type.toLowerCase()) && addresses) {
            for (const addr of addresses) {
              if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                return addr.address;
              }
            }
          }
        }
      }

      // Fallback: any non-internal IPv4 address
      for (const addresses of Object.values(interfaces)) {
        if (addresses) {
          for (const addr of addresses) {
            if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
              return addr.address;
            }
          }
        }
      }

      return 'localhost';
    } catch (error) {
      logger.warn('Error detecting server IP:', error);
      return 'localhost';
    }
  }

  /**
   * Get system information
   */
  private getSystemInfo(): any {
    return {
      platform: os.platform(),
      hostname: os.hostname(),
      username: os.userInfo().username,
      nodeVersion: process.version
    };
  }

  /**
   * Generate unique deployment ID
   */
  private generateDeploymentId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    const hostname = os.hostname().substring(0, 8).toLowerCase();
    
    return `vms-${hostname}-${timestamp}-${random}`;
  }

  /**
   * Get deployment configuration
   */
  public getDeploymentConfig(): DeploymentConfig | null {
    return this._deploymentConfig;
  }

  /**
   * Get deployment paths
   */
  public getDeploymentPaths(): DeploymentPaths | null {
    return this._deploymentConfig?.paths || null;
  }

  /**
   * Check if service is initialized
   */
  public isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * Resolve path relative to VMS root
   */
  public resolvePath(...pathSegments: string[]): string {
    if (!this._deploymentConfig) {
      throw new Error('Portable deployment service not initialized');
    }
    
    return path.resolve(this._deploymentConfig.paths.vmsRoot, ...pathSegments);
  }

  /**
   * Get absolute path for a relative path
   */
  public getAbsolutePath(relativePath: string): string {
    if (!this._deploymentConfig) {
      throw new Error('Portable deployment service not initialized');
    }
    
    if (path.isAbsolute(relativePath)) {
      return relativePath;
    }
    
    return path.resolve(this._deploymentConfig.paths.vmsRoot, relativePath);
  }
}

// Export singleton instance
export const portableDeploymentService = new PortableDeploymentService();
