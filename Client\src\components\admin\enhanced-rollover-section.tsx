import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Save, RefreshCw, Calendar, Clock, Database, Shield, Zap, Timer, Play, Pause } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

interface RolloverSettings {
  currentFiscalYear: number;
  systemTime: string;
  useLiveTime?: boolean; // Optional until migration completes
  autoRolloverEnabled?: boolean; // Optional until migration completes
  scheduledRolloverDate?: string;
}

export function EnhancedRolloverSection() {
  const [settings, setSettings] = useState<RolloverSettings>({
    currentFiscalYear: new Date().getFullYear(),
    systemTime: new Date().toISOString(),
    useLiveTime: true, // Default to true
    autoRolloverEnabled: true, // Default to true
    scheduledRolloverDate: undefined
  });

  const [liveTime, setLiveTime] = useState(new Date().toISOString());
  const [isLoading, setIsLoading] = useState(false);
  const [rolloverStatus, setRolloverStatus] = useState<any>(null);
  const [serverTimeInfo, setServerTimeInfo] = useState<any>(null);

  // Update live time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setLiveTime(new Date().toISOString());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
    loadRolloverStatus();
    loadServerTimeInfo();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSettings({
          currentFiscalYear: data.current_fiscal_year,
          systemTime: data.system_time,
          useLiveTime: data.use_live_time ?? true,
          autoRolloverEnabled: data.auto_rollover_enabled ?? true,
          scheduledRolloverDate: data.scheduled_rollover_date
        });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      toast.error('Failed to load rollover settings');
    }
  };

  const loadRolloverStatus = async () => {
    try {
      const response = await fetch('/api/admin/year-rollover/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRolloverStatus(data);
      }
    } catch (error) {
      console.error('Failed to load rollover status:', error);
    }
  };

  const loadServerTimeInfo = async () => {
    try {
      const response = await fetch('/api/admin/server-time-info', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setServerTimeInfo(data);
      }
    } catch (error) {
      console.error('Failed to load server time info:', error);
    }
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          current_fiscal_year: settings.currentFiscalYear,
          system_time: settings.systemTime,
          use_live_time: settings.useLiveTime,
          auto_rollover_enabled: settings.autoRolloverEnabled
        })
      });

      if (response.ok) {
        toast.success('Rollover settings saved successfully');
        await loadRolloverStatus();
        await loadServerTimeInfo();
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Save settings error:', error);
      toast.error('Failed to save rollover settings');
    } finally {
      setIsLoading(false);
    }
  };

  const resetToLiveTime = async () => {
    try {
      const response = await fetch('/api/admin/reset-system-time', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(prev => ({
          ...prev,
          systemTime: data.systemTime,
          useLiveTime: true
        }));
        toast.success('System time reset to live server time');
        await loadRolloverStatus();
        await loadServerTimeInfo();
      } else {
        throw new Error('Failed to reset system time');
      }
    } catch (error) {
      console.error('Reset time error:', error);
      toast.error('Failed to reset system time');
    }
  };

  const triggerImmediateRollover = async () => {
    if (!confirm('Are you sure you want to trigger an immediate rollover to the next fiscal year? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/year-rollover/immediate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        toast.success('Immediate rollover completed successfully');
        await loadSettings();
        await loadRolloverStatus();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Rollover failed');
      }
    } catch (error) {
      console.error('Immediate rollover error:', error);
      toast.error(`Immediate rollover failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const scheduleRollover = async () => {
    if (!settings.scheduledRolloverDate) {
      toast.error('Please select a rollover date');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/year-rollover/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          rolloverDate: settings.scheduledRolloverDate
        })
      });

      if (response.ok) {
        toast.success('Rollover scheduled successfully');
        await loadRolloverStatus();
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to schedule rollover');
      }
    } catch (error) {
      console.error('Schedule rollover error:', error);
      toast.error(`Failed to schedule rollover: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const cancelScheduledRollover = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/year-rollover/schedule', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setSettings(prev => ({ ...prev, scheduledRolloverDate: undefined }));
        toast.success('Scheduled rollover cancelled');
        await loadRolloverStatus();
      } else {
        throw new Error('Failed to cancel scheduled rollover');
      }
    } catch (error) {
      console.error('Cancel rollover error:', error);
      toast.error('Failed to cancel scheduled rollover');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTimeDifference = () => {
    const systemTime = new Date(settings.systemTime);
    const currentTime = new Date();
    const diffMs = Math.abs(systemTime.getTime() - currentTime.getTime());
    const diffMinutes = Math.round(diffMs / 60000);
    return diffMinutes;
  };

  const isTimeOverridden = () => {
    return !settings.useLiveTime && getTimeDifference() > 5;
  };

  return (
    <div className="space-y-6">
      {/* Live Time Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Live Time Monitor
          </CardTitle>
          <CardDescription>
            Real-time server time and system configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Live Server Time</Label>
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="font-mono text-sm text-green-800">
                  {formatDateTime(liveTime)}
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>System Time Setting</Label>
              <div className={`p-3 border rounded-md ${
                settings.useLiveTime ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'
              }`}>
                <div className={`font-mono text-sm ${
                  settings.useLiveTime ? 'text-green-800' : 'text-yellow-800'
                }`}>
                  {formatDateTime(settings.systemTime)}
                </div>
                {isTimeOverridden() && (
                  <div className="text-xs text-yellow-600 mt-1">
                    Override active ({getTimeDifference()} min difference)
                  </div>
                )}
                {serverTimeInfo && (
                  <div className="text-xs mt-2 space-y-1">
                    <div className={`${settings.useLiveTime ? 'text-green-600' : 'text-yellow-600'}`}>
                      Sync Status: {settings.useLiveTime ? 'Live Sync Enabled' : 'Manual Override'}
                    </div>
                    <div className="text-gray-600">
                      Time Zone: {serverTimeInfo.timeZone}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                checked={settings.useLiveTime}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, useLiveTime: checked }))}
              />
              <Label>Use Live Server Time</Label>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={resetToLiveTime}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset to Live Time
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Rollover Status */}
      {rolloverStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Rollover Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Current Fiscal Year</Label>
                <div className="text-2xl font-bold">{rolloverStatus.currentFiscalYear}</div>
              </div>
              <div>
                <Label>Next Fiscal Year</Label>
                <div className="text-2xl font-bold">{rolloverStatus.nextFiscalYear}</div>
              </div>
              <div>
                <Label>Days Until Rollover</Label>
                <div className="text-2xl font-bold">{rolloverStatus.daysUntilRollover}</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={rolloverStatus.rolloverNeeded ? "destructive" : "secondary"}>
                {rolloverStatus.rolloverNeeded ? "Rollover Needed" : "Current"}
              </Badge>
              {rolloverStatus.isRolloverInProgress && (
                <Badge variant="outline">Rollover In Progress</Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rollover Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Rollover Controls
          </CardTitle>
          <CardDescription>
            Manage automatic and manual year rollover operations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto Rollover Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Automatic Rollover</Label>
              <p className="text-sm text-muted-foreground">
                Enable automatic rollover on fiscal year transition
              </p>
            </div>
            <Switch
              checked={settings.autoRolloverEnabled}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoRolloverEnabled: checked }))}
            />
          </div>

          <Separator />

          {/* Immediate Rollover */}
          <div className="space-y-3">
            <Label>Immediate Rollover</Label>
            <p className="text-sm text-muted-foreground">
              Trigger an immediate rollover to the next fiscal year
            </p>
            <Button
              onClick={triggerImmediateRollover}
              disabled={isLoading}
              variant="destructive"
              className="w-full"
            >
              <Play className="h-4 w-4 mr-2" />
              Trigger Immediate Rollover
            </Button>
          </div>

          <Separator />

          {/* Scheduled Rollover */}
          <div className="space-y-3">
            <Label>Schedule Rollover</Label>
            <p className="text-sm text-muted-foreground">
              Schedule rollover for a specific date and time
            </p>
            
            <div className="flex gap-2">
              <Input
                type="datetime-local"
                value={settings.scheduledRolloverDate ? new Date(settings.scheduledRolloverDate).toISOString().slice(0, 16) : ''}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  scheduledRolloverDate: e.target.value ? new Date(e.target.value).toISOString() : undefined 
                }))}
                className="flex-1"
              />
              <Button
                onClick={scheduleRollover}
                disabled={isLoading || !settings.scheduledRolloverDate}
                variant="outline"
              >
                <Timer className="h-4 w-4 mr-2" />
                Schedule
              </Button>
            </div>

            {settings.scheduledRolloverDate && (
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div>
                  <div className="font-medium text-blue-800">Rollover Scheduled</div>
                  <div className="text-sm text-blue-600">
                    {formatDateTime(settings.scheduledRolloverDate)}
                  </div>
                </div>
                <Button
                  onClick={cancelScheduledRollover}
                  disabled={isLoading}
                  variant="outline"
                  size="sm"
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            )}
          </div>

          <Separator />

          {/* Save Settings */}
          <Button
            onClick={saveSettings}
            disabled={isLoading}
            className="w-full"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Rollover Settings
          </Button>
        </CardContent>
      </Card>

      {/* LAN Time Management Notice */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-600">
            <Clock className="h-5 w-5" />
            LAN Time Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="text-sm text-blue-800">
              <strong>LAN-Based System:</strong> VMS operates independently without internet dependency for reliable time management.
            </div>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Advantage:</strong> No internet connectivity required - system operates reliably on LAN
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Time Source:</strong> Uses server computer time for consistent operations
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Best Practice:</strong> Verify server time accuracy daily using external reference
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Manual Control:</strong> Full admin control over time settings and rollover timing
              </div>
            </div>
          </div>

          {serverTimeInfo && (
            <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="text-sm text-green-800">
                <strong>Current LAN Server Time:</strong>
              </div>
              <div className="text-xs text-green-700 mt-1 space-y-1">
                <div>Local Time: {serverTimeInfo.localTime}</div>
                <div>Time Zone: {serverTimeInfo.timeZone}</div>
                <div>Status: LAN-Based (No Internet Required)</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Warnings */}
      {isTimeOverridden() && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            System time override is active. Automatic rollover will be skipped until live time is enabled.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
