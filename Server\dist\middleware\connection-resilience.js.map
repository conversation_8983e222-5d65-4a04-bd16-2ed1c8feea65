{"version": 3, "file": "connection-resilience.js", "sourceRoot": "", "sources": ["../../src/middleware/connection-resilience.ts"], "names": [], "mappings": ";AAAA,wCAAwC;AACxC,mEAAmE;;;AAkInE,sDAiBC;AAjJD,4CAAyC;AASzC,MAAM,uBAAuB;IACnB,mBAAmB,GAAoC,IAAI,GAAG,EAAE,CAAC;IACxD,YAAY,GAAG,KAAK,CAAC,CAAC,0BAA0B;IAChD,qBAAqB,GAAG,KAAK,CAAC,CAAC,8CAA8C;IACtF,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IAEzD,+DAA+D;IACxD,mBAAmB,CACxB,MAAc,EACd,QAAgB,EAChB,UAAkB,EAClB,QAAgB,EAChB,eAAoC;QAEpC,MAAM,QAAQ,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAC;QAEzC,iDAAiD;QACjD,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAExC,eAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,YAAY,GAAC,IAAI,sBAAsB,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC;QAE3G,4BAA4B;QAC5B,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YAClC,eAAM,CAAC,IAAI,CAAC,0CAA0C,QAAQ,KAAK,MAAM,6BAA6B,CAAC,CAAC;YAExG,IAAI,CAAC;gBACH,MAAM,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAEtB,kBAAkB;QAClB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE;YACrC,MAAM;YACN,QAAQ;YACR,UAAU;YACV,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IACzC,wBAAwB,CAAC,QAAgB;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,mDAAmD,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2BAA2B;IACpB,kBAAkB,CAAC,MAAc,EAAE,QAAgB,EAAE,QAAgB;QAC1E,MAAM,QAAQ,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE1D,IAAI,SAAS,EAAE,CAAC;YACd,eAAM,CAAC,IAAI,CAAC,uBAAuB,QAAQ,KAAK,MAAM,mCAAmC,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,uBAAuB,QAAQ,KAAK,MAAM,wCAAwC,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED,yCAAyC;IAClC,sBAAsB,CAAC,QAAgB;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE1D,IAAI,GAAG,GAAG,SAAS,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAClD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,wCAAwC;IACjC,yBAAyB;QAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,mBAAmB;QAE5E,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YACnE,IAAI,GAAG,GAAG,SAAS,GAAG,gBAAgB,EAAE,CAAC;gBACvC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED,4BAA4B;IACrB,kBAAkB;QACvB,OAAO;YACL,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI;YACpD,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;YAC7C,aAAa,EAAE,IAAI,CAAC,YAAY;YAChC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;SAClD,CAAC;IACJ,CAAC;IAED,2CAA2C;IACpC,OAAO;QACZ,eAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAEhE,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;YACvE,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9B,eAAM,CAAC,IAAI,CAAC,oCAAoC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAErE,gDAAgD;AAChD,SAAgB,qBAAqB,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS;IACjE,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC,CAAC;IAEzE,0CAA0C;IAC1C,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QACzD,oCAAoC;QACpC,IAAI,CAAC,+BAAuB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,gDAAgD;YAChD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC;AAED,wBAAwB;AACxB,WAAW,CAAC,GAAG,EAAE;IACf,+BAAuB,CAAC,yBAAyB,EAAE,CAAC;AACtD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,wBAAwB"}