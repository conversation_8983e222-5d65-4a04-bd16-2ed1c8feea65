// VMS Connection Resilience System
// Production-grade offline/online handling with data sync

import { useAppStore } from './store';
import { realTimeSyncManager } from './real-time-sync-manager';

interface PendingOperation {
  id: string;
  type: 'CREATE_VOUCHER' | 'UPDATE_VOUCHER' | 'DELETE_VOUCHER' | 'BATCH_ACTION';
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

class ConnectionResilienceManager {
  private isOnline: boolean = navigator.onLine;
  private pendingOperations: PendingOperation[] = [];
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000; // Start with 1 second
  private maxReconnectDelay: number = 30000; // Max 30 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private syncInProgress: boolean = false;

  constructor() {
    this.initializeConnectionHandling();
    this.loadPendingOperations();
  }

  private initializeConnectionHandling() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    
    // Listen for beforeunload to save pending operations
    window.addEventListener('beforeunload', this.savePendingOperations.bind(this));
    
    console.log('🔄 Connection resilience manager initialized');
  }

  private handleOffline() {
    console.log('📡 Connection lost - entering offline mode');
    this.isOnline = false;
    this.stopHealthChecks();
    
    // Show offline indicator
    this.showConnectionStatus('offline');
    
    // Stop any ongoing sync
    this.syncInProgress = false;
  }

  private handleOnline() {
    console.log('📡 Connection restored - entering online mode');
    this.isOnline = true;
    this.reconnectAttempts = 0;
    
    // Show online indicator
    this.showConnectionStatus('online');
    
    // Start gradual reconnection
    this.startGradualReconnection();
  }

  private async startGradualReconnection() {
    console.log('🔄 Starting gradual reconnection process...');
    
    // Wait a moment for connection to stabilize
    await this.delay(2000);
    
    try {
      // Test connection with health check
      const isHealthy = await this.testConnection();
      
      if (isHealthy) {
        console.log('✅ Connection stable - starting data sync');
        await this.syncPendingOperations();
        this.startOptimizedHealthChecks();
      } else {
        console.log('⚠️ Connection unstable - retrying...');
        this.scheduleReconnectAttempt();
      }
    } catch (error) {
      console.error('❌ Reconnection failed:', error);
      this.scheduleReconnectAttempt();
    }
  }

  private async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  private scheduleReconnectAttempt() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached - manual refresh required');
      this.showConnectionStatus('failed');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      this.maxReconnectDelay
    );

    console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (navigator.onLine) {
        this.startGradualReconnection();
      }
    }, delay);
  }

  private startOptimizedHealthChecks() {
    // Stop any existing health checks
    this.stopHealthChecks();
    
    // Start optimized health checks (every 30 seconds instead of continuous)
    this.healthCheckInterval = setInterval(async () => {
      if (!this.isOnline) return;
      
      const isHealthy = await this.testConnection();
      if (!isHealthy) {
        console.log('⚠️ Health check failed - connection may be unstable');
        this.handleOffline();
      }
    }, 30000); // 30 seconds
    
    console.log('🏥 Optimized health checks started (30s interval)');
  }

  private stopHealthChecks() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('🏥 Health checks stopped');
    }
  }

  // Queue operations for offline execution
  public queueOperation(operation: Omit<PendingOperation, 'id' | 'timestamp' | 'retryCount'>) {
    const pendingOp: PendingOperation = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0,
      ...operation
    };

    this.pendingOperations.push(pendingOp);
    this.savePendingOperations();
    
    console.log(`📝 Queued ${operation.type} operation for sync:`, pendingOp.id);
    
    // If online, try to sync immediately
    if (this.isOnline && !this.syncInProgress) {
      this.syncPendingOperations();
    }
  }

  private async syncPendingOperations() {
    if (this.syncInProgress || this.pendingOperations.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`🔄 Syncing ${this.pendingOperations.length} pending operations...`);

    const operationsToSync = [...this.pendingOperations];
    const successfulOperations: string[] = [];

    for (const operation of operationsToSync) {
      try {
        const success = await this.executeOperation(operation);
        
        if (success) {
          successfulOperations.push(operation.id);
          console.log(`✅ Synced operation: ${operation.type} (${operation.id})`);
        } else {
          operation.retryCount++;
          if (operation.retryCount >= operation.maxRetries) {
            console.error(`❌ Operation failed after ${operation.maxRetries} retries: ${operation.id}`);
            successfulOperations.push(operation.id); // Remove failed operations
          }
        }
      } catch (error) {
        console.error(`❌ Error syncing operation ${operation.id}:`, error);
        operation.retryCount++;
      }
    }

    // Remove successful operations
    this.pendingOperations = this.pendingOperations.filter(
      op => !successfulOperations.includes(op.id)
    );

    this.savePendingOperations();
    this.syncInProgress = false;

    if (successfulOperations.length > 0) {
      console.log(`✅ Successfully synced ${successfulOperations.length} operations`);

      // PRODUCTION FIX: Trigger real-time UI refresh
      await realTimeSyncManager.manualRefresh();

      // Dispatch sync completion event
      window.dispatchEvent(new CustomEvent('vms-sync-complete', {
        detail: {
          syncedCount: successfulOperations.length,
          failedCount: operationsToSync.length - successfulOperations.length
        }
      }));

      // Add sync events for each successful operation
      operationsToSync
        .filter(op => successfulOperations.includes(op.id))
        .forEach(op => {
          realTimeSyncManager.addSyncEvent({
            type: op.type === 'CREATE_VOUCHER' ? 'VOUCHER_SYNCED' :
                  op.type === 'BATCH_ACTION' ? 'BATCH_SYNCED' : 'STATUS_UPDATED',
            data: op.data,
            timestamp: Date.now()
          });
        });
    }
  }

  private async executeOperation(operation: PendingOperation): Promise<boolean> {
    try {
      switch (operation.type) {
        case 'CREATE_VOUCHER':
          const response = await fetch('/api/vouchers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(operation.data),
            credentials: 'include'
          });
          return response.ok;

        case 'UPDATE_VOUCHER':
          const updateResponse = await fetch(`/api/vouchers/${operation.data.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(operation.data),
            credentials: 'include'
          });
          return updateResponse.ok;

        case 'BATCH_ACTION':
          const batchResponse = await fetch('/api/batches', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(operation.data),
            credentials: 'include'
          });
          return batchResponse.ok;

        default:
          console.warn(`Unknown operation type: ${operation.type}`);
          return false;
      }
    } catch (error) {
      console.error(`Error executing ${operation.type}:`, error);
      return false;
    }
  }

  private savePendingOperations() {
    try {
      localStorage.setItem('vms_pending_operations', JSON.stringify(this.pendingOperations));
    } catch (error) {
      console.error('Failed to save pending operations:', error);
    }
  }

  private loadPendingOperations() {
    try {
      const saved = localStorage.getItem('vms_pending_operations');
      if (saved) {
        this.pendingOperations = JSON.parse(saved);
        console.log(`📝 Loaded ${this.pendingOperations.length} pending operations from storage`);
      }
    } catch (error) {
      console.error('Failed to load pending operations:', error);
      this.pendingOperations = [];
    }
  }

  private showConnectionStatus(status: 'online' | 'offline' | 'failed') {
    // Remove any existing status indicators
    const existingIndicator = document.getElementById('connection-status');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    // Create status indicator
    const indicator = document.createElement('div');
    indicator.id = 'connection-status';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 8px 16px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      z-index: 10000;
      font-size: 14px;
    `;

    switch (status) {
      case 'online':
        indicator.style.backgroundColor = '#10b981';
        indicator.textContent = '🟢 Online';
        setTimeout(() => indicator.remove(), 3000); // Auto-hide after 3s
        break;
      case 'offline':
        indicator.style.backgroundColor = '#f59e0b';
        indicator.textContent = '🟡 Offline - Data will sync when reconnected';
        break;
      case 'failed':
        indicator.style.backgroundColor = '#ef4444';
        indicator.textContent = '🔴 Connection failed - Please refresh page';
        break;
    }

    document.body.appendChild(indicator);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Public methods
  public isConnectionOnline(): boolean {
    return this.isOnline;
  }

  public getPendingOperationsCount(): number {
    return this.pendingOperations.length;
  }

  public forceSyncNow(): Promise<void> {
    if (this.isOnline) {
      return this.syncPendingOperations();
    }
    return Promise.resolve();
  }
}

// Export singleton instance
export const connectionManager = new ConnectionResilienceManager();
