@echo off
echo VMS Automated Deployment System Installation
echo ==========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Installing VMS Automated Deployment System...
echo.

REM Run the Auto-Deploy application
if exist "VMS-Auto-Deploy.exe" (
    echo Starting VMS Auto-Deploy...
    start /wait "VMS Auto-Deploy" "VMS-Auto-Deploy.exe"
) else (
    echo ERROR: VMS-Auto-Deploy.exe not found
    pause
    exit /b 1
)

echo.
echo Installation completed!
echo VMS system should now be running and accessible.
echo.
pause
