using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace VMSADMIN
{
    /// <summary>
    /// VMS-ADMIN Main Window
    /// Desktop client for VMS system administration
    /// </summary>
    public partial class MainWindow : Window
    {
        private NetworkDiscoveryClient _networkDiscovery;
        private string _vmsServerURL;
        private string _adminDashboardURL;
        private bool _isConnecting = false;
        private DispatcherTimer _discoveryTimer;

        public MainWindow()
        {
            InitializeComponent();
            InitializeVMSAdmin();
        }

        /// <summary>
        /// Initialize VMS-ADMIN client
        /// </summary>
        private async void InitializeVMSAdmin()
        {
            try
            {
                LogStatus("🔧 Initializing VMS-ADMIN...", "Starting system administration client");
                UpdateProgress(10);

                // Initialize network discovery
                _networkDiscovery = new NetworkDiscoveryClient();
                _networkDiscovery.ServerDiscovered += OnServerDiscovered;
                _networkDiscovery.LogMessage += OnLogMessage;

                LogStatus("🔍 Starting server discovery...", "Scanning network for VMS server");
                UpdateProgress(30);

                // Start network discovery
                await _networkDiscovery.StartDiscoveryAsync();

                LogStatus("📡 Network discovery active", "Searching for VMS server and admin service");
                UpdateProgress(50);

                // Start periodic discovery updates
                StartPeriodicDiscovery();

                LogStatus("✅ VMS-ADMIN ready", "Waiting for VMS server detection");
                UpdateProgress(100);

            }
            catch (Exception ex)
            {
                LogError($"❌ Initialization failed: {ex.Message}");
                ShowErrorDialog("VMS-ADMIN Initialization Error", 
                    $"Failed to initialize VMS-ADMIN client:\n\n{ex.Message}\n\nPlease check your network connection and try again.");
            }
        }

        /// <summary>
        /// Handle server discovery events
        /// </summary>
        private async void OnServerDiscovered(object sender, ServerDiscoveredEventArgs e)
        {
            try
            {
                await Dispatcher.InvokeAsync(() =>
                {
                    var server = e.Server;
                    _vmsServerURL = $"http://{server.ServerIP}:{server.ServerPort}";
                    _adminDashboardURL = $"http://{server.ServerIP}:8081"; // Admin service on port 8081

                    LogStatus("✅ VMS server detected!", $"Found server at {server.ServerIP}:{server.ServerPort}");
                    
                    NetworkStatusText.Text = "✅ VMS server found";
                    ServerInfoText.Text = $"Server: {server.ServerIP}:{server.ServerPort} ({server.Version})";
                    
                    // Test admin service availability
                    _ = Task.Run(TestAdminServiceAsync);
                });
            }
            catch (Exception ex)
            {
                LogError($"Error handling server discovery: {ex.Message}");
            }
        }

        /// <summary>
        /// Test if admin service is available
        /// </summary>
        private async Task TestAdminServiceAsync()
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    var response = await client.GetAsync($"{_adminDashboardURL}/health");
                    
                    await Dispatcher.InvokeAsync(() =>
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            AdminServiceText.Text = "✅ Admin Service: Available";
                            ConnectButton.IsEnabled = true;
                            LogStatus("🎛️ Admin dashboard ready", "Click 'Connect to Dashboard' to access admin interface");
                            FooterStatusText.Text = "Admin dashboard available - ready to connect";
                        }
                        else
                        {
                            AdminServiceText.Text = "⚠️ Admin Service: Not responding";
                            LogStatus("⚠️ Admin service not available", "VMS server found but admin dashboard not accessible");
                        }
                    });
                }
            }
            catch (Exception)
            {
                await Dispatcher.InvokeAsync(() =>
                {
                    AdminServiceText.Text = "❌ Admin Service: Not available";
                    LogStatus("❌ Admin service unavailable", "Admin dashboard service not running on server");
                });
            }
        }

        /// <summary>
        /// Handle network discovery log messages
        /// </summary>
        private void OnLogMessage(object sender, string message)
        {
            Dispatcher.InvokeAsync(() =>
            {
                // Update detail text with latest discovery message
                if (message.Contains("Discovery request sent"))
                {
                    DetailText.Text = "Scanning network for VMS server...";
                }
                else if (message.Contains("Server announcement received"))
                {
                    DetailText.Text = "VMS server detected - verifying admin service...";
                }
                else if (message.Contains("accessible and healthy"))
                {
                    DetailText.Text = "VMS server verified - admin dashboard ready";
                }
            });
        }

        /// <summary>
        /// Start periodic discovery updates
        /// </summary>
        private void StartPeriodicDiscovery()
        {
            _discoveryTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(30) // Check every 30 seconds
            };
            
            _discoveryTimer.Tick += async (sender, e) =>
            {
                if (!_isConnecting && _networkDiscovery.IsRunning)
                {
                    // Refresh server list
                    var servers = _networkDiscovery.GetDiscoveredServers();
                    if (servers.Count == 0)
                    {
                        NetworkStatusText.Text = "🔍 Searching for VMS server...";
                        ServerInfoText.Text = "Server: Not detected";
                        AdminServiceText.Text = "Admin Service: Waiting for server";
                        ConnectButton.IsEnabled = false;
                    }
                }
            };
            
            _discoveryTimer.Start();
        }

        /// <summary>
        /// Connect to admin dashboard
        /// </summary>
        private async void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_adminDashboardURL))
            {
                ShowErrorDialog("Connection Error", "No VMS server detected. Please wait for server discovery or check your network connection.");
                return;
            }

            try
            {
                _isConnecting = true;
                ConnectButton.IsEnabled = false;
                
                LogStatus("🌐 Opening admin dashboard...", "Launching web browser for VMS administration");
                UpdateProgress(0);

                // Test connection one more time
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10);
                    UpdateProgress(30);
                    
                    var response = await client.GetAsync($"{_adminDashboardURL}/health");
                    UpdateProgress(60);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        LogStatus("✅ Admin service verified", "Opening dashboard in default browser");
                        UpdateProgress(80);
                        
                        // Open browser to admin dashboard
                        Process.Start(new ProcessStartInfo(_adminDashboardURL)
                        {
                            UseShellExecute = true
                        });
                        
                        UpdateProgress(100);
                        LogStatus("🎛️ Dashboard opened", "VMS administration interface launched in browser");
                        FooterStatusText.Text = "Admin dashboard opened in browser - you can close this window";
                        
                        // Auto-close after 3 seconds
                        await Task.Delay(3000);
                        Application.Current.Shutdown();
                    }
                    else
                    {
                        throw new Exception($"Admin service returned status: {response.StatusCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Connection failed: {ex.Message}");
                ShowErrorDialog("Connection Error", 
                    $"Failed to connect to VMS admin dashboard:\n\n{ex.Message}\n\nPlease ensure the VMS server is running and try again.");
            }
            finally
            {
                _isConnecting = false;
                ConnectButton.IsEnabled = !string.IsNullOrEmpty(_adminDashboardURL);
            }
        }

        /// <summary>
        /// Refresh server discovery
        /// </summary>
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                RefreshButton.IsEnabled = false;
                LogStatus("🔄 Refreshing server discovery...", "Scanning network for VMS server");
                
                // Reset status
                NetworkStatusText.Text = "🔍 Scanning for VMS server...";
                ServerInfoText.Text = "Server: Scanning...";
                AdminServiceText.Text = "Admin Service: Waiting...";
                ConnectButton.IsEnabled = false;
                
                // Restart discovery
                _networkDiscovery?.StopDiscovery();
                await Task.Delay(1000);
                await _networkDiscovery?.StartDiscoveryAsync();
                
                LogStatus("📡 Discovery refreshed", "Searching for VMS server and admin service");
            }
            catch (Exception ex)
            {
                LogError($"❌ Refresh failed: {ex.Message}");
            }
            finally
            {
                RefreshButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Open settings dialog
        /// </summary>
        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.Owner = this;
            settingsWindow.ShowDialog();
        }

        /// <summary>
        /// Log status message
        /// </summary>
        private void LogStatus(string status, string detail)
        {
            StatusText.Text = status;
            DetailText.Text = detail;
        }

        /// <summary>
        /// Log error message
        /// </summary>
        private void LogError(string error)
        {
            StatusText.Text = error;
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            DetailText.Text = "Check network connection and VMS server status";
        }

        /// <summary>
        /// Update progress bar
        /// </summary>
        private void UpdateProgress(double value)
        {
            ProgressBar.Value = value;
            if (value >= 100)
            {
                ProgressBar.IsIndeterminate = false;
            }
        }

        /// <summary>
        /// Show error dialog
        /// </summary>
        private void ShowErrorDialog(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// Window closing event
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _discoveryTimer?.Stop();
                _networkDiscovery?.StopDiscovery();
            }
            catch { }
            
            base.OnClosed(e);
        }
    }
}
