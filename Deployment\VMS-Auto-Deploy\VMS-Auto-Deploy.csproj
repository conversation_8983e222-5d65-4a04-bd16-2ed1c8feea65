<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>VMSAutoDeploy</RootNamespace>
    <AssemblyName>VMS-Auto-Deploy</AssemblyName>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>VMS-Auto-Deploy.ico</ApplicationIcon>
    <AssemblyTitle>VMS Auto-Deploy</AssemblyTitle>
    <AssemblyDescription>Automated zero-configuration deployment system for VMS Production</AssemblyDescription>
    <AssemblyCompany>VMS Production Team</AssemblyCompany>
    <AssemblyProduct>VMS Auto-Deploy</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2025 VMS Production Team</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="7.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="VMS-Auto-Deploy.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
