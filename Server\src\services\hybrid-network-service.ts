/**
 * Hybrid Network Management Service
 * Combines static IP assignment with network discovery backup
 * Provides bulletproof network handling for production deployment
 */

import { EventEmitter } from 'events';
import { networkDiscoveryService } from './network-discovery-service.js';
import { logger } from '../utils/logger.js';

export interface NetworkConfiguration {
  mode: 'static' | 'dynamic' | 'hybrid';
  staticIP: string | null;
  dynamicIP: string | null;
  currentIP: string;
  port: number;
  isStaticAvailable: boolean;
  isDynamicAvailable: boolean;
  lastNetworkChange: Date;
  networkAdapter: string | null;
  subnetMask: string | null;
  gateway: string | null;
  dnsServers: string[];
}

export interface StaticIPConfig {
  targetIP: string;
  subnetMask: string;
  gateway: string;
  dnsServers: string[];
  adapterName: string;
}

export interface NetworkChangeEvent {
  type: 'static_failed' | 'static_restored' | 'dynamic_activated' | 'network_changed' | 'ip_conflict';
  oldConfig: NetworkConfiguration;
  newConfig: NetworkConfiguration;
  timestamp: Date;
  reason: string;
}

export class HybridNetworkService extends EventEmitter {
  private _config: NetworkConfiguration;
  private _monitoringInterval: NodeJS.Timeout | null = null;
  private _isRunning = false;
  private _healthCheckInterval = 30000; // 30 seconds
  private _networkChangeDetectionInterval = 10000; // 10 seconds

  constructor(port: number = 8080) {
    super();
    
    this._config = {
      mode: 'hybrid',
      staticIP: null,
      dynamicIP: null,
      currentIP: 'localhost',
      port,
      isStaticAvailable: false,
      isDynamicAvailable: false,
      lastNetworkChange: new Date(),
      networkAdapter: null,
      subnetMask: null,
      gateway: null,
      dnsServers: []
    };

    logger.info('🌐 Hybrid Network Service initialized');
  }

  /**
   * Start hybrid network management
   */
  public async start(): Promise<void> {
    try {
      if (this._isRunning) {
        logger.warn('Hybrid Network Service is already running');
        return;
      }

      logger.info('🚀 Starting Hybrid Network Management System...');

      // Phase 1: Analyze current network
      await this.analyzeCurrentNetwork();

      // Phase 2: Attempt static IP assignment
      await this.attemptStaticIPAssignment();

      // Phase 3: Initialize network discovery as backup
      await this.initializeNetworkDiscovery();

      // Phase 4: Start continuous monitoring
      this.startNetworkMonitoring();

      this._isRunning = true;
      
      logger.info('✅ Hybrid Network Management System started successfully');
      logger.info(`🖥️ Current Mode: ${this._config.mode}`);
      logger.info(`📍 Active IP: ${this._config.currentIP}:${this._config.port}`);
      logger.info(`🔧 Static IP Available: ${this._config.isStaticAvailable}`);
      logger.info(`🔍 Dynamic Discovery Available: ${this._config.isDynamicAvailable}`);

    } catch (error) {
      logger.error('❌ Failed to start Hybrid Network Service:', error);
      throw error;
    }
  }

  /**
   * Stop hybrid network management
   */
  public async stop(): Promise<void> {
    try {
      if (!this._isRunning) {
        return;
      }

      logger.info('🛑 Stopping Hybrid Network Management System...');

      // Stop monitoring
      if (this._monitoringInterval) {
        clearInterval(this._monitoringInterval);
        this._monitoringInterval = null;
      }

      // Stop network discovery
      await networkDiscoveryService.stop();

      this._isRunning = false;
      logger.info('✅ Hybrid Network Management System stopped');

    } catch (error) {
      logger.error('❌ Error stopping Hybrid Network Service:', error);
    }
  }

  /**
   * Analyze current network configuration
   */
  private async analyzeCurrentNetwork(): Promise<void> {
    try {
      logger.info('🔍 Analyzing current network configuration...');

      // Get current network information
      const networkInfo = await this.getCurrentNetworkInfo();
      
      this._config.dynamicIP = networkInfo.currentIP;
      this._config.currentIP = networkInfo.currentIP;
      this._config.networkAdapter = networkInfo.adapterName;
      this._config.subnetMask = networkInfo.subnetMask;
      this._config.gateway = networkInfo.gateway;
      this._config.dnsServers = networkInfo.dnsServers;
      this._config.isDynamicAvailable = true;

      logger.info(`📍 Current IP: ${networkInfo.currentIP}`);
      logger.info(`🔌 Network Adapter: ${networkInfo.adapterName}`);
      logger.info(`🌐 Gateway: ${networkInfo.gateway}`);
      logger.info(`🔍 Subnet: ${networkInfo.subnetMask}`);

    } catch (error) {
      logger.error('❌ Error analyzing network:', error);
      throw error;
    }
  }

  /**
   * Attempt to assign static IP
   */
  private async attemptStaticIPAssignment(): Promise<void> {
    try {
      logger.info('🔧 Attempting static IP assignment...');

      // Find optimal static IP
      const staticIPConfig = await this.findOptimalStaticIP();
      
      if (!staticIPConfig) {
        logger.warn('⚠️ No suitable static IP found - using dynamic mode');
        this._config.mode = 'dynamic';
        return;
      }

      // Attempt to assign static IP
      const success = await this.assignStaticIP(staticIPConfig);
      
      if (success) {
        this._config.staticIP = staticIPConfig.targetIP;
        this._config.currentIP = staticIPConfig.targetIP;
        this._config.isStaticAvailable = true;
        this._config.mode = 'static';
        
        logger.info(`✅ Static IP assigned successfully: ${staticIPConfig.targetIP}`);
      } else {
        logger.warn('⚠️ Static IP assignment failed - falling back to dynamic mode');
        this._config.mode = 'dynamic';
      }

    } catch (error) {
      logger.warn('⚠️ Static IP assignment error - using dynamic mode:', error);
      this._config.mode = 'dynamic';
    }
  }

  /**
   * Initialize network discovery as backup
   */
  private async initializeNetworkDiscovery(): Promise<void> {
    try {
      logger.info('🔍 Initializing network discovery backup system...');

      // Configure network discovery with current IP
      await networkDiscoveryService.start();
      
      this._config.isDynamicAvailable = true;
      
      logger.info('✅ Network discovery backup system ready');

    } catch (error) {
      logger.error('❌ Failed to initialize network discovery:', error);
      this._config.isDynamicAvailable = false;
    }
  }

  /**
   * Start continuous network monitoring
   */
  private startNetworkMonitoring(): void {
    logger.info('👁️ Starting continuous network monitoring...');

    this._monitoringInterval = setInterval(async () => {
      try {
        await this.performNetworkHealthCheck();
      } catch (error) {
        logger.error('❌ Network health check error:', error);
      }
    }, this._healthCheckInterval);

    logger.info(`⏰ Network monitoring active (${this._healthCheckInterval / 1000}s intervals)`);
  }

  /**
   * Perform network health check
   */
  private async performNetworkHealthCheck(): Promise<void> {
    try {
      const oldConfig = { ...this._config };
      let configChanged = false;

      // Check static IP availability
      if (this._config.staticIP) {
        const staticAvailable = await this.testIPConnectivity(this._config.staticIP);
        
        if (staticAvailable !== this._config.isStaticAvailable) {
          this._config.isStaticAvailable = staticAvailable;
          configChanged = true;

          if (staticAvailable) {
            logger.info('✅ Static IP restored - switching back to static mode');
            this._config.currentIP = this._config.staticIP;
            this._config.mode = 'static';
            this.emitNetworkChange('static_restored', oldConfig, this._config, 'Static IP connectivity restored');
          } else {
            logger.warn('⚠️ Static IP failed - switching to dynamic mode');
            await this.switchToDynamicMode();
            this.emitNetworkChange('static_failed', oldConfig, this._config, 'Static IP connectivity lost');
          }
        }
      }

      // Check for network configuration changes
      const currentNetworkInfo = await this.getCurrentNetworkInfo();
      if (currentNetworkInfo.currentIP !== this._config.dynamicIP) {
        logger.info(`🔄 Network change detected: ${this._config.dynamicIP} → ${currentNetworkInfo.currentIP}`);
        
        this._config.dynamicIP = currentNetworkInfo.currentIP;
        this._config.lastNetworkChange = new Date();
        configChanged = true;

        // If in dynamic mode, update current IP
        if (this._config.mode === 'dynamic') {
          this._config.currentIP = currentNetworkInfo.currentIP;
        }

        this.emitNetworkChange('network_changed', oldConfig, this._config, 'Network configuration changed');
      }

      // Update network discovery if needed
      if (configChanged && this._config.isDynamicAvailable) {
        // Network discovery service will automatically pick up the new IP
        logger.debug('🔄 Network discovery will adapt to changes automatically');
      }

    } catch (error) {
      logger.error('❌ Network health check failed:', error);
    }
  }

  /**
   * Switch to dynamic mode
   */
  private async switchToDynamicMode(): Promise<void> {
    try {
      this._config.mode = 'dynamic';
      
      if (this._config.dynamicIP) {
        this._config.currentIP = this._config.dynamicIP;
      } else {
        const networkInfo = await this.getCurrentNetworkInfo();
        this._config.currentIP = networkInfo.currentIP;
        this._config.dynamicIP = networkInfo.currentIP;
      }

      logger.info(`🔄 Switched to dynamic mode - using IP: ${this._config.currentIP}`);

    } catch (error) {
      logger.error('❌ Error switching to dynamic mode:', error);
    }
  }

  /**
   * Test IP connectivity
   */
  private async testIPConnectivity(ip: string): Promise<boolean> {
    try {
      // Simple ping test using Node.js
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      const pingCommand = process.platform === 'win32' 
        ? `ping -n 1 -w 1000 ${ip}`
        : `ping -c 1 -W 1 ${ip}`;

      await execAsync(pingCommand);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Emit network change event
   */
  private emitNetworkChange(
    type: NetworkChangeEvent['type'], 
    oldConfig: NetworkConfiguration, 
    newConfig: NetworkConfiguration, 
    reason: string
  ): void {
    const event: NetworkChangeEvent = {
      type,
      oldConfig,
      newConfig,
      timestamp: new Date(),
      reason
    };

    this.emit('networkChange', event);
    logger.info(`📡 Network change event: ${type} - ${reason}`);
  }

  /**
   * Get current network configuration
   */
  public getNetworkConfiguration(): NetworkConfiguration {
    return { ...this._config };
  }

  /**
   * Get current server URL
   */
  public getServerURL(): string {
    return `http://${this._config.currentIP}:${this._config.port}`;
  }

  /**
   * Check if service is running
   */
  public isRunning(): boolean {
    return this._isRunning;
  }

  /**
   * Force switch to static mode (if available)
   */
  public async switchToStaticMode(): Promise<boolean> {
    if (!this._config.staticIP || !this._config.isStaticAvailable) {
      return false;
    }

    const oldConfig = { ...this._config };
    this._config.mode = 'static';
    this._config.currentIP = this._config.staticIP;

    this.emitNetworkChange('static_restored', oldConfig, this._config, 'Manual switch to static mode');
    return true;
  }

  /**
   * Force switch to dynamic mode
   */
  public async switchToDynamicModeManual(): Promise<boolean> {
    const oldConfig = { ...this._config };
    await this.switchToDynamicMode();

    this.emitNetworkChange('dynamic_activated', oldConfig, this._config, 'Manual switch to dynamic mode');
    return true;
  }

  // Integration with Static IP Assignment Service
  private async getCurrentNetworkInfo(): Promise<any> {
    const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
    return await staticIPAssignmentService.getCurrentNetworkInfo();
  }

  private async findOptimalStaticIP(): Promise<StaticIPConfig | null> {
    const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
    return await staticIPAssignmentService.findOptimalStaticIP();
  }

  private async assignStaticIP(config: StaticIPConfig): Promise<boolean> {
    const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
    const result = await staticIPAssignmentService.assignStaticIP(config);
    return result.success;
  }
}

// Export singleton instance
export const hybridNetworkService = new HybridNetworkService();
