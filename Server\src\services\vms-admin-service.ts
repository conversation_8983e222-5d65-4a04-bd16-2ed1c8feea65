/**
 * VMS-ADMIN Service
 * Web-based administration dashboard service
 * Runs on port 8081 alongside VMS server (port 8080)
 */

import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';
import bcrypt from 'bcrypt';
import session from 'express-session';
import { networkIntegrationService } from './network-integration-service.js';
import { hybridNetworkService } from './hybrid-network-service.js';
import { intelligentFallbackService } from './intelligent-fallback-service.js';
import { selfHealingNetworkMonitor } from './self-healing-network-monitor.js';
import { logger } from '../utils/logger.js';
import { db } from '../database/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export interface AdminUser {
  id: number;
  username: string;
  role: string;
  department: string;
  permissions: string[];
  lastLogin: Date;
}

export interface DashboardStats {
  systemStatus: 'healthy' | 'degraded' | 'critical' | 'offline';
  vmsServer: {
    status: 'running' | 'stopped' | 'error';
    uptime: number;
    port: number;
    connections: number;
  };
  networkSystem: {
    mode: 'static' | 'dynamic' | 'hybrid';
    staticIP: string | null;
    dynamicIP: string | null;
    connectivity: boolean;
    lastChange: Date;
  };
  database: {
    status: 'connected' | 'disconnected' | 'error';
    size: number;
    lastBackup: Date | null;
  };
  users: {
    totalActive: number;
    adminSessions: number;
    departmentBreakdown: Record<string, number>;
  };
}

export class VMSAdminService {
  private _app: express.Application;
  private _server: any;
  private _io: SocketIOServer;
  private _isRunning = false;
  private _port = 8081;
  private _connectedAdmins: Map<string, AdminUser> = new Map();

  constructor() {
    this._app = express();
    this._server = createServer(this._app);
    this._io = new SocketIOServer(this._server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
    
    logger.info('🎛️ VMS-ADMIN Service initialized');
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Session management
    this._app.use(session({
      name: 'vms-admin-session',
      secret: process.env.ADMIN_SESSION_SECRET || 'vms-admin-secret-key-change-in-production',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: false, // Set to true for HTTPS
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      }
    }));

    // Body parsing
    this._app.use(express.json());
    this._app.use(express.urlencoded({ extended: true }));

    // Static files for dashboard UI
    const dashboardPath = path.join(__dirname, '../../dashboard');
    this._app.use(express.static(dashboardPath));

    // CORS for API endpoints
    this._app.use('/api', (req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    logger.info('🔧 VMS-ADMIN middleware configured');
  }

  /**
   * Setup Express routes
   */
  private setupRoutes(): void {
    // Health check endpoint
    this._app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'VMS-ADMIN',
        version: '1.0.0',
        timestamp: new Date(),
        uptime: process.uptime()
      });
    });

    // Authentication routes
    this._app.post('/api/auth/login', this.handleLogin.bind(this));
    this._app.post('/api/auth/logout', this.handleLogout.bind(this));
    this._app.get('/api/auth/status', this.handleAuthStatus.bind(this));

    // Protected API routes (require admin authentication)
    this._app.use('/api', this.requireAdminAuth.bind(this));

    // Dashboard data endpoints
    this._app.get('/api/dashboard/stats', this.getDashboardStats.bind(this));
    this._app.get('/api/dashboard/system-status', this.getSystemStatus.bind(this));
    this._app.get('/api/dashboard/network-status', this.getNetworkStatus.bind(this));

    // System control endpoints
    this._app.post('/api/system/restart-vms', this.restartVMSServer.bind(this));
    this._app.post('/api/system/switch-network-mode', this.switchNetworkMode.bind(this));
    this._app.post('/api/system/trigger-healing', this.triggerHealing.bind(this));

    // Database management endpoints
    this._app.get('/api/database/status', this.getDatabaseStatus.bind(this));
    this._app.post('/api/database/backup', this.createDatabaseBackup.bind(this));
    this._app.get('/api/database/backups', this.listDatabaseBackups.bind(this));

    // User management endpoints
    this._app.get('/api/users/active', this.getActiveUsers.bind(this));
    this._app.get('/api/users/sessions', this.getUserSessions.bind(this));

    // Logs endpoints
    this._app.get('/api/logs/recent', this.getRecentLogs.bind(this));
    this._app.get('/api/logs/download', this.downloadLogs.bind(this));

    // Default route - serve dashboard HTML
    this._app.get('/', (req, res) => {
      if (req.session && (req.session as any).adminUser) {
        res.sendFile(path.join(__dirname, '../../dashboard/index.html'));
      } else {
        res.sendFile(path.join(__dirname, '../../dashboard/login.html'));
      }
    });

    logger.info('🛣️ VMS-ADMIN routes configured');
  }

  /**
   * Setup Socket.IO handlers for real-time updates
   */
  private setupSocketHandlers(): void {
    this._io.on('connection', (socket) => {
      logger.info(`🔌 Admin client connected: ${socket.id}`);

      // Authenticate socket connection
      socket.on('authenticate', async (data) => {
        try {
          const { sessionId } = data;
          // Verify session and get admin user
          const adminUser = await this.getAdminUserBySession(sessionId);
          
          if (adminUser) {
            socket.data.adminUser = adminUser;
            socket.join('admin-room');
            socket.emit('authenticated', { success: true, user: adminUser });
            
            // Send initial dashboard data
            const stats = await this.collectDashboardStats();
            socket.emit('dashboard-stats', stats);
            
            logger.info(`✅ Admin authenticated via socket: ${adminUser.username}`);
          } else {
            socket.emit('authenticated', { success: false, error: 'Invalid session' });
          }
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Authentication failed' });
        }
      });

      // Handle real-time requests
      socket.on('request-stats', async () => {
        if (socket.data.adminUser) {
          const stats = await this.collectDashboardStats();
          socket.emit('dashboard-stats', stats);
        }
      });

      socket.on('disconnect', () => {
        logger.info(`🔌 Admin client disconnected: ${socket.id}`);
      });
    });

    // Setup real-time event broadcasting
    this.setupRealTimeUpdates();

    logger.info('🔌 VMS-ADMIN Socket.IO configured');
  }

  /**
   * Setup real-time updates from network services
   */
  private setupRealTimeUpdates(): void {
    // Listen to network integration events
    networkIntegrationService.on('networkChange', (event) => {
      this._io.to('admin-room').emit('network-change', event);
    });

    networkIntegrationService.on('fallbackDecision', (decision) => {
      this._io.to('admin-room').emit('fallback-decision', decision);
    });

    networkIntegrationService.on('healingAction', (action) => {
      this._io.to('admin-room').emit('healing-action', action);
    });

    // Periodic stats updates
    setInterval(async () => {
      try {
        const stats = await this.collectDashboardStats();
        this._io.to('admin-room').emit('dashboard-stats', stats);
      } catch (error) {
        logger.error('Error broadcasting stats update:', error);
      }
    }, 30000); // Every 30 seconds

    logger.info('📡 Real-time updates configured');
  }

  /**
   * Handle admin login
   */
  private async handleLogin(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        res.status(400).json({ success: false, error: 'Username and password required' });
        return;
      }

      // Query VMS database for admin user
      const user = await db.get(`
        SELECT id, username, password_hash, role, department, permissions, status 
        FROM users 
        WHERE username = ? AND role IN ('admin', 'super_admin') AND status = 'active'
      `, [username]);

      if (!user) {
        res.status(401).json({ success: false, error: 'Invalid credentials or insufficient permissions' });
        return;
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        res.status(401).json({ success: false, error: 'Invalid credentials' });
        return;
      }

      // Create admin session
      const adminUser: AdminUser = {
        id: user.id,
        username: user.username,
        role: user.role,
        department: user.department,
        permissions: user.permissions ? JSON.parse(user.permissions) : [],
        lastLogin: new Date()
      };

      (req.session as any).adminUser = adminUser;
      this._connectedAdmins.set(req.sessionID, adminUser);

      // Update last login in database
      await db.run('UPDATE users SET last_login = ? WHERE id = ?', [new Date(), user.id]);

      logger.info(`✅ Admin login successful: ${username} (${user.role})`);
      
      res.json({
        success: true,
        user: {
          id: adminUser.id,
          username: adminUser.username,
          role: adminUser.role,
          department: adminUser.department,
          permissions: adminUser.permissions
        }
      });

    } catch (error) {
      logger.error('Admin login error:', error);
      res.status(500).json({ success: false, error: 'Login failed' });
    }
  }

  /**
   * Handle admin logout
   */
  private async handleLogout(req: express.Request, res: express.Response): Promise<void> {
    try {
      const sessionId = req.sessionID;
      
      if (this._connectedAdmins.has(sessionId)) {
        const adminUser = this._connectedAdmins.get(sessionId);
        this._connectedAdmins.delete(sessionId);
        logger.info(`👋 Admin logout: ${adminUser?.username}`);
      }

      req.session.destroy((err) => {
        if (err) {
          logger.error('Session destruction error:', err);
        }
      });

      res.json({ success: true });

    } catch (error) {
      logger.error('Admin logout error:', error);
      res.status(500).json({ success: false, error: 'Logout failed' });
    }
  }

  /**
   * Handle authentication status check
   */
  private async handleAuthStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const adminUser = (req.session as any)?.adminUser;
      
      if (adminUser) {
        res.json({
          authenticated: true,
          user: {
            id: adminUser.id,
            username: adminUser.username,
            role: adminUser.role,
            department: adminUser.department,
            permissions: adminUser.permissions
          }
        });
      } else {
        res.json({ authenticated: false });
      }

    } catch (error) {
      logger.error('Auth status check error:', error);
      res.status(500).json({ authenticated: false, error: 'Status check failed' });
    }
  }

  /**
   * Require admin authentication middleware
   */
  private requireAdminAuth(req: express.Request, res: express.Response, next: express.NextFunction): void {
    const adminUser = (req.session as any)?.adminUser;
    
    if (adminUser && (adminUser.role === 'admin' || adminUser.role === 'super_admin')) {
      next();
    } else {
      res.status(401).json({ success: false, error: 'Admin authentication required' });
    }
  }

  /**
   * Get dashboard statistics
   */
  private async getDashboardStats(req: express.Request, res: express.Response): Promise<void> {
    try {
      const stats = await this.collectDashboardStats();
      res.json(stats);
    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      res.status(500).json({ error: 'Failed to get dashboard stats' });
    }
  }

  /**
   * Collect comprehensive dashboard statistics
   */
  private async collectDashboardStats(): Promise<DashboardStats> {
    try {
      // Get system status from network integration service
      const systemStatus = await networkIntegrationService.getSystemStatus();
      const networkConfig = hybridNetworkService.getNetworkConfiguration();
      
      // Get database status
      const dbStatus = await this.getDatabaseStatusInfo();
      
      // Get user statistics
      const userStats = await this.getUserStatistics();

      return {
        systemStatus: systemStatus.overall,
        vmsServer: {
          status: systemStatus.components.hybridNetwork ? 'running' : 'stopped',
          uptime: process.uptime() * 1000,
          port: 8080,
          connections: this._connectedAdmins.size
        },
        networkSystem: {
          mode: networkConfig.mode,
          staticIP: networkConfig.staticIP,
          dynamicIP: networkConfig.dynamicIP,
          connectivity: networkConfig.isStaticAvailable || networkConfig.isDynamicAvailable,
          lastChange: networkConfig.lastNetworkChange
        },
        database: dbStatus,
        users: userStats
      };

    } catch (error) {
      logger.error('Error collecting dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Get database status information
   */
  private async getDatabaseStatusInfo(): Promise<any> {
    try {
      // Test database connection
      await db.get('SELECT 1');
      
      // Get database size (simplified)
      const tables = await db.all("SELECT name FROM sqlite_master WHERE type='table'");
      
      return {
        status: 'connected',
        size: tables.length,
        lastBackup: null // TODO: Implement backup tracking
      };
    } catch (error) {
      return {
        status: 'error',
        size: 0,
        lastBackup: null
      };
    }
  }

  /**
   * Get user statistics
   */
  private async getUserStatistics(): Promise<any> {
    try {
      const activeUsers = await db.all(`
        SELECT department, COUNT(*) as count 
        FROM users 
        WHERE status = 'active' 
        GROUP BY department
      `);

      const departmentBreakdown: Record<string, number> = {};
      let totalActive = 0;

      activeUsers.forEach(row => {
        departmentBreakdown[row.department] = row.count;
        totalActive += row.count;
      });

      return {
        totalActive,
        adminSessions: this._connectedAdmins.size,
        departmentBreakdown
      };

    } catch (error) {
      logger.error('Error getting user statistics:', error);
      return {
        totalActive: 0,
        adminSessions: 0,
        departmentBreakdown: {}
      };
    }
  }

  /**
   * Get system status
   */
  private async getSystemStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const status = await networkIntegrationService.getSystemStatus();
      res.json(status);
    } catch (error) {
      logger.error('Error getting system status:', error);
      res.status(500).json({ error: 'Failed to get system status' });
    }
  }

  /**
   * Get network status
   */
  private async getNetworkStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const networkConfig = hybridNetworkService.getNetworkConfiguration();
      const fallbackStats = intelligentFallbackService.getStats();
      const healingStats = selfHealingNetworkMonitor.getStats();

      res.json({
        configuration: networkConfig,
        fallbackStats,
        healingStats
      });
    } catch (error) {
      logger.error('Error getting network status:', error);
      res.status(500).json({ error: 'Failed to get network status' });
    }
  }

  /**
   * Restart VMS server
   */
  private async restartVMSServer(req: express.Request, res: express.Response): Promise<void> {
    try {
      const success = await networkIntegrationService.restartServices();
      res.json({ success, message: success ? 'VMS server restarted successfully' : 'Failed to restart VMS server' });
    } catch (error) {
      logger.error('Error restarting VMS server:', error);
      res.status(500).json({ success: false, error: 'Failed to restart VMS server' });
    }
  }

  /**
   * Switch network mode
   */
  private async switchNetworkMode(req: express.Request, res: express.Response): Promise<void> {
    try {
      const { mode } = req.body;

      if (!mode || !['static', 'dynamic'].includes(mode)) {
        res.status(400).json({ success: false, error: 'Invalid network mode' });
        return;
      }

      const success = await networkIntegrationService.switchNetworkMode(mode);
      res.json({
        success,
        message: success ? `Switched to ${mode} mode successfully` : `Failed to switch to ${mode} mode`
      });
    } catch (error) {
      logger.error('Error switching network mode:', error);
      res.status(500).json({ success: false, error: 'Failed to switch network mode' });
    }
  }

  /**
   * Trigger healing
   */
  private async triggerHealing(req: express.Request, res: express.Response): Promise<void> {
    try {
      const success = await networkIntegrationService.triggerHealing();
      res.json({
        success,
        message: success ? 'Network healing triggered successfully' : 'Failed to trigger network healing'
      });
    } catch (error) {
      logger.error('Error triggering healing:', error);
      res.status(500).json({ success: false, error: 'Failed to trigger healing' });
    }
  }

  /**
   * Get database status
   */
  private async getDatabaseStatus(req: express.Request, res: express.Response): Promise<void> {
    try {
      const status = await this.getDatabaseStatusInfo();
      res.json(status);
    } catch (error) {
      logger.error('Error getting database status:', error);
      res.status(500).json({ error: 'Failed to get database status' });
    }
  }

  /**
   * Create database backup
   */
  private async createDatabaseBackup(req: express.Request, res: express.Response): Promise<void> {
    try {
      // TODO: Implement database backup functionality
      res.json({ success: false, message: 'Database backup functionality not yet implemented' });
    } catch (error) {
      logger.error('Error creating database backup:', error);
      res.status(500).json({ success: false, error: 'Failed to create database backup' });
    }
  }

  /**
   * List database backups
   */
  private async listDatabaseBackups(req: express.Request, res: express.Response): Promise<void> {
    try {
      // TODO: Implement backup listing functionality
      res.json([]);
    } catch (error) {
      logger.error('Error listing database backups:', error);
      res.status(500).json({ error: 'Failed to list database backups' });
    }
  }

  /**
   * Get active users
   */
  private async getActiveUsers(req: express.Request, res: express.Response): Promise<void> {
    try {
      const users = await db.all(`
        SELECT id, username, role, department, last_login, status
        FROM users
        WHERE status = 'active'
        ORDER BY last_login DESC
      `);

      res.json(users);
    } catch (error) {
      logger.error('Error getting active users:', error);
      res.status(500).json({ error: 'Failed to get active users' });
    }
  }

  /**
   * Get user sessions
   */
  private async getUserSessions(req: express.Request, res: express.Response): Promise<void> {
    try {
      const sessions = Array.from(this._connectedAdmins.values()).map(user => ({
        username: user.username,
        role: user.role,
        department: user.department,
        lastLogin: user.lastLogin
      }));

      res.json(sessions);
    } catch (error) {
      logger.error('Error getting user sessions:', error);
      res.status(500).json({ error: 'Failed to get user sessions' });
    }
  }

  /**
   * Get recent logs
   */
  private async getRecentLogs(req: express.Request, res: express.Response): Promise<void> {
    try {
      // TODO: Implement log reading functionality
      res.json([]);
    } catch (error) {
      logger.error('Error getting recent logs:', error);
      res.status(500).json({ error: 'Failed to get recent logs' });
    }
  }

  /**
   * Download logs
   */
  private async downloadLogs(req: express.Request, res: express.Response): Promise<void> {
    try {
      // TODO: Implement log download functionality
      res.status(501).json({ error: 'Log download not yet implemented' });
    } catch (error) {
      logger.error('Error downloading logs:', error);
      res.status(500).json({ error: 'Failed to download logs' });
    }
  }

  /**
   * Start VMS-ADMIN service
   */
  public async start(): Promise<void> {
    try {
      if (this._isRunning) {
        logger.warn('VMS-ADMIN Service is already running');
        return;
      }

      return new Promise((resolve, reject) => {
        this._server.listen(this._port, () => {
          this._isRunning = true;
          logger.info(`🎛️ VMS-ADMIN Service started on port ${this._port}`);
          logger.info(`🌐 Admin dashboard available at: http://localhost:${this._port}`);
          resolve();
        });

        this._server.on('error', (error: any) => {
          logger.error(`❌ VMS-ADMIN Service failed to start: ${error.message}`);
          reject(error);
        });
      });

    } catch (error) {
      logger.error('❌ Failed to start VMS-ADMIN Service:', error);
      throw error;
    }
  }

  /**
   * Stop VMS-ADMIN service
   */
  public async stop(): Promise<void> {
    try {
      if (!this._isRunning) {
        return;
      }

      return new Promise((resolve) => {
        this._server.close(() => {
          this._isRunning = false;
          this._connectedAdmins.clear();
          logger.info('🛑 VMS-ADMIN Service stopped');
          resolve();
        });
      });

    } catch (error) {
      logger.error('❌ Error stopping VMS-ADMIN Service:', error);
    }
  }

  /**
   * Get admin user by session ID
   */
  private async getAdminUserBySession(sessionId: string): Promise<AdminUser | null> {
    return this._connectedAdmins.get(sessionId) || null;
  }

  /**
   * Check if service is running
   */
  public isRunning(): boolean {
    return this._isRunning;
  }

  /**
   * Get service port
   */
  public getPort(): number {
    return this._port;
  }
}

// Export singleton instance
export const vmsAdminService = new VMSAdminService();
