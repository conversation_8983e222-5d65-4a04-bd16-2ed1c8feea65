import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, Clock, CheckCircle, AlertTriangle, Wifi, WifiOff } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

export function LANTimeManager() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [manualTime, setManualTime] = useState({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
    hour: new Date().getHours(),
    minute: new Date().getMinutes()
  });
  const [timeVerified, setTimeVerified] = useState(false);
  const [lastVerification, setLastVerification] = useState<string | null>(null);

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Load last verification time
  useEffect(() => {
    const lastVerified = localStorage.getItem('vms_time_verified');
    if (lastVerified) {
      setLastVerification(lastVerified);
      // Consider time verified if done within last 24 hours
      const verifiedTime = new Date(lastVerified);
      const now = new Date();
      const hoursSince = (now.getTime() - verifiedTime.getTime()) / (1000 * 60 * 60);
      setTimeVerified(hoursSince < 24);
    }
  }, []);

  const verifyCurrentTime = () => {
    const now = new Date().toISOString();
    localStorage.setItem('vms_time_verified', now);
    setLastVerification(now);
    setTimeVerified(true);
    toast.success('Server time verified as accurate');
  };

  const setManualCorrection = async () => {
    try {
      const correctedTime = new Date(
        manualTime.year,
        manualTime.month - 1,
        manualTime.day,
        manualTime.hour,
        manualTime.minute
      );

      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          system_time: correctedTime.toISOString(),
          use_live_time: false // Use manual override
        })
      });

      if (response.ok) {
        toast.success('Manual time correction applied');
        localStorage.setItem('vms_time_verified', new Date().toISOString());
        setTimeVerified(true);
      } else {
        throw new Error('Failed to apply time correction');
      }
    } catch (error) {
      toast.error('Failed to apply manual time correction');
    }
  };

  const resetToComputerTime = async () => {
    try {
      const response = await fetch('/api/admin/reset-system-time', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        toast.success('Reset to computer time');
        localStorage.setItem('vms_time_verified', new Date().toISOString());
        setTimeVerified(true);
      } else {
        throw new Error('Failed to reset time');
      }
    } catch (error) {
      toast.error('Failed to reset to computer time');
    }
  };

  const getTimeStatus = () => {
    if (!lastVerification) {
      return { status: 'unverified', color: 'red', message: 'Time has never been verified' };
    }

    const verifiedTime = new Date(lastVerification);
    const now = new Date();
    const hoursSince = (now.getTime() - verifiedTime.getTime()) / (1000 * 60 * 60);

    if (hoursSince < 1) {
      return { status: 'verified', color: 'green', message: 'Recently verified (< 1 hour ago)' };
    } else if (hoursSince < 24) {
      return { status: 'good', color: 'blue', message: `Verified ${Math.round(hoursSince)} hours ago` };
    } else {
      return { status: 'stale', color: 'orange', message: `Last verified ${Math.round(hoursSince / 24)} days ago` };
    }
  };

  const timeStatus = getTimeStatus();

  return (
    <div className="space-y-6">
      {/* LAN Time Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <WifiOff className="h-5 w-5 text-blue-600" />
            LAN Time Management
          </CardTitle>
          <CardDescription>
            Manage system time in LAN environment (no internet required)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Time Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Current Server Computer Time</Label>
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="font-mono text-lg text-blue-800">
                  {currentTime.toLocaleString()}
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  {currentTime.toISOString()}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Time Verification Status</Label>
              <div className={`p-3 border rounded-md ${
                timeStatus.color === 'green' ? 'bg-green-50 border-green-200' :
                timeStatus.color === 'blue' ? 'bg-blue-50 border-blue-200' :
                timeStatus.color === 'orange' ? 'bg-orange-50 border-orange-200' :
                'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center gap-2">
                  {timeStatus.status === 'verified' ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : timeStatus.status === 'good' ? (
                    <Clock className="h-4 w-4 text-blue-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                  )}
                  <span className={`text-sm font-medium ${
                    timeStatus.color === 'green' ? 'text-green-800' :
                    timeStatus.color === 'blue' ? 'text-blue-800' :
                    timeStatus.color === 'orange' ? 'text-orange-800' :
                    'text-red-800'
                  }`}>
                    {timeStatus.message}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Verification Actions */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={verifyCurrentTime}
              variant="outline"
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Verify Current Time is Accurate
            </Button>
            
            <Button
              onClick={resetToComputerTime}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Clock className="h-4 w-4" />
              Use Computer Time
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Manual Time Correction */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Time Correction</CardTitle>
          <CardDescription>
            If computer time is incorrect, set the correct time manually
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            <div className="space-y-1">
              <Label className="text-xs">Year</Label>
              <Select
                value={manualTime.year.toString()}
                onValueChange={(value) => setManualTime(prev => ({ ...prev, year: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({length: 10}, (_, i) => {
                    const year = new Date().getFullYear() - 2 + i;
                    return (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label className="text-xs">Month</Label>
              <Select
                value={manualTime.month.toString()}
                onValueChange={(value) => setManualTime(prev => ({ ...prev, month: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({length: 12}, (_, i) => {
                    const month = i + 1;
                    const monthName = new Date(2000, i, 1).toLocaleString('default', { month: 'short' });
                    return (
                      <SelectItem key={month} value={month.toString()}>
                        {month.toString().padStart(2, '0')} - {monthName}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label className="text-xs">Day</Label>
              <Select
                value={manualTime.day.toString()}
                onValueChange={(value) => setManualTime(prev => ({ ...prev, day: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({length: 31}, (_, i) => {
                    const day = i + 1;
                    return (
                      <SelectItem key={day} value={day.toString()}>
                        {day.toString().padStart(2, '0')}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label className="text-xs">Hour</Label>
              <Select
                value={manualTime.hour.toString()}
                onValueChange={(value) => setManualTime(prev => ({ ...prev, hour: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({length: 24}, (_, i) => (
                    <SelectItem key={i} value={i.toString()}>
                      {i.toString().padStart(2, '0')}:00
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label className="text-xs">Minute</Label>
              <Select
                value={manualTime.minute.toString()}
                onValueChange={(value) => setManualTime(prev => ({ ...prev, minute: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({length: 60}, (_, i) => (
                    <SelectItem key={i} value={i.toString()}>
                      {i.toString().padStart(2, '0')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
            <div className="text-sm font-medium text-gray-700">Preview:</div>
            <div className="font-mono text-gray-800">
              {new Date(manualTime.year, manualTime.month - 1, manualTime.day, manualTime.hour, manualTime.minute).toLocaleString()}
            </div>
          </div>

          <Button
            onClick={setManualCorrection}
            className="w-full"
          >
            Apply Manual Time Correction
          </Button>
        </CardContent>
      </Card>

      {/* LAN Best Practices */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-600">
            <AlertCircle className="h-5 w-5" />
            LAN Time Management Best Practices
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Daily Verification:</strong> Verify server time accuracy at least once daily
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Reference Source:</strong> Use a reliable external time source (radio, phone, watch) for verification
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Before Rollover:</strong> Always verify time accuracy before important rollover periods
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>Manual Correction:</strong> Use manual correction only when computer time is definitely wrong
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <strong>LAN Advantage:</strong> No internet dependency means consistent, predictable time management
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
