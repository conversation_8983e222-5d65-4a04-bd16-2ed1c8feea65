// Live Time Synchronization Service for LAN Environment
import { query } from '../database/db';
import { logger } from '../utils/logger';

class LiveTimeSyncService {
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL_MS = 60000; // Sync every minute

  /**
   * Start the live time synchronization service
   */
  public start(): void {
    if (this.syncInterval) {
      logger.warn('Live time sync service is already running');
      return;
    }

    logger.info('🕐 Starting live time synchronization service');
    
    // Initial sync
    this.syncSystemTime();
    
    // Set up periodic sync
    this.syncInterval = setInterval(() => {
      this.syncSystemTime();
    }, this.SYNC_INTERVAL_MS);

    logger.info(`🕐 Live time sync service started (interval: ${this.SYNC_INTERVAL_MS / 1000}s)`);
  }

  /**
   * Stop the live time synchronization service
   */
  public stop(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      logger.info('🕐 Live time synchronization service stopped');
    }
  }

  /**
   * Sync system time to current computer time if live time is enabled
   */
  private async syncSystemTime(): Promise<void> {
    try {
      // Get current system settings
      const settings = await query('SELECT * FROM system_settings LIMIT 1') as any[];

      if (!settings || settings.length === 0) {
        logger.warn('No system settings found for time sync');
        return;
      }

      const currentSettings = settings[0];

      if (!currentSettings) {
        logger.warn('System settings record is empty');
        return;
      }

      // Handle case where use_live_time column might not exist yet
      const useLiveTime = currentSettings.hasOwnProperty('use_live_time')
        ? currentSettings.use_live_time !== false
        : true; // Default to true if column doesn't exist

      if (!useLiveTime) {
        // Live time is disabled, don't sync
        logger.debug('Live time sync skipped - use_live_time is disabled');
        return;
      }

      const currentTime = new Date();
      const storedTime = new Date(currentSettings.system_time);
      const timeDifference = Math.abs(currentTime.getTime() - storedTime.getTime());

      // Only update if there's a significant difference (more than 30 seconds)
      if (timeDifference > 30000) {
        await query(
          'UPDATE system_settings SET system_time = ? WHERE id = ?',
          [currentTime.toISOString(), currentSettings.id]
        );

        logger.info(`🕐 Live time sync: Updated system_time from ${storedTime.toISOString()} to ${currentTime.toISOString()}`);
      } else {
        logger.debug('Live time sync: No update needed (time difference < 30s)');
      }

    } catch (error) {
      logger.error('Live time sync error:', error);
    }
  }

  /**
   * Force immediate sync (for manual triggers)
   */
  public async forceSyncNow(): Promise<boolean> {
    try {
      const currentTime = new Date();
      
      await query(
        'UPDATE system_settings SET system_time = ?, use_live_time = ? WHERE id = 1',
        [currentTime.toISOString(), true]
      );

      logger.info(`🕐 Force sync: Updated system_time to ${currentTime.toISOString()}`);
      return true;
    } catch (error) {
      logger.error('Force sync error:', error);
      return false;
    }
  }

  /**
   * Get sync status information
   */
  public async getSyncStatus(): Promise<any> {
    try {
      const [settings] = await query('SELECT * FROM system_settings LIMIT 1') as any[];
      
      if (!settings || settings.length === 0) {
        return { error: 'No system settings found' };
      }

      const currentSettings = settings[0];
      const currentTime = new Date();
      const storedTime = new Date(currentSettings.system_time);
      const timeDifference = Math.abs(currentTime.getTime() - storedTime.getTime());

      // Handle case where use_live_time column might not exist yet
      const useLiveTime = currentSettings.hasOwnProperty('use_live_time')
        ? currentSettings.use_live_time !== false
        : true; // Default to true if column doesn't exist

      return {
        currentTime: currentTime.toISOString(),
        storedTime: storedTime.toISOString(),
        timeDifference: timeDifference,
        timeDifferenceMinutes: Math.round(timeDifference / 60000),
        useLiveTime: useLiveTime,
        syncActive: useLiveTime,
        lastSyncTime: useLiveTime ? currentTime.toISOString() : null,
        syncInterval: this.SYNC_INTERVAL_MS / 1000
      };
    } catch (error) {
      logger.error('Get sync status error:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

// Export singleton instance
export const liveTimeSyncService = new LiveTimeSyncService();
