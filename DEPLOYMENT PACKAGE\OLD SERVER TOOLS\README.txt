VMS SERVER MANAGEMENT TOOLS
============================

This directory contains batch scripts for managing the VMS server after deployment.

AVAILABLE SCRIPTS
-----------------

RESTART.bat
- Restarts the VMS server service
- Use when server needs to be restarted
- Automatically stops and starts the service

START-VMS-SINGLE.bat  
- Starts VMS server in single-instance mode
- Use for testing or manual server startup
- Runs server directly (not as Windows Service)

STATUS.bat
- Shows current status of VMS server
- Displays service status and port information
- Use to check if server is running properly

STOP.bat
- Stops the VMS server service
- Use when server needs to be shut down
- Gracefully stops all VMS processes

USAGE INSTRUCTIONS
------------------

1. Run these scripts as Administrator for best results
2. Use STATUS.bat first to check current server state
3. Use RESTART.bat for routine server maintenance
4. Use STOP.bat before system maintenance
5. Use START-VMS-SINGLE.bat for troubleshooting

These tools are provided for server administrators to manage
the VMS system after deployment is complete.
