/**
 * Self-Healing Network Monitor
 * Provides continuous network monitoring with automatic adaptation to changes
 * Implements proactive network health management and recovery
 */

import { EventEmitter } from 'events';
import { hybridNetworkService } from './hybrid-network-service.js';
import { intelligentFallbackService } from './intelligent-fallback-service.js';
import { networkDiscoveryService } from './network-discovery-service.js';
import { logger } from '../utils/logger.js';

export interface NetworkHealthMetrics {
  timestamp: Date;
  connectivity: {
    staticIPReachable: boolean;
    dynamicIPReachable: boolean;
    internetReachable: boolean;
    gatewayReachable: boolean;
    dnsResolution: boolean;
  };
  performance: {
    latency: number;
    packetLoss: number;
    bandwidth: number;
    responseTime: number;
  };
  stability: {
    ipChanges: number;
    connectionDrops: number;
    recoveryTime: number;
    uptime: number;
  };
  services: {
    vmsServerHealthy: boolean;
    networkDiscoveryActive: boolean;
    fallbackSystemActive: boolean;
  };
}

export interface HealingAction {
  id: string;
  type: 'restart_service' | 'switch_mode' | 'reset_adapter' | 'flush_dns' | 'force_discovery';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  executedAt: Date;
  success: boolean;
  error?: string;
  metrics: NetworkHealthMetrics;
}

export interface MonitoringConfig {
  healthCheckInterval: number;
  connectivityTimeout: number;
  performanceThresholds: {
    maxLatency: number;
    maxPacketLoss: number;
    minBandwidth: number;
    maxResponseTime: number;
  };
  healingThresholds: {
    consecutiveFailures: number;
    criticalLatency: number;
    criticalPacketLoss: number;
  };
  enabled: boolean;
}

export class SelfHealingNetworkMonitor extends EventEmitter {
  private _isRunning = false;
  private _monitoringInterval: NodeJS.Timeout | null = null;
  private _healthHistory: NetworkHealthMetrics[] = [];
  private _healingHistory: HealingAction[] = [];
  private _consecutiveFailures = 0;
  private _lastHealthCheck: Date | null = null;
  private _startTime = new Date();

  private _config: MonitoringConfig = {
    healthCheckInterval: 30000, // 30 seconds
    connectivityTimeout: 5000,  // 5 seconds
    performanceThresholds: {
      maxLatency: 100,        // 100ms
      maxPacketLoss: 5,       // 5%
      minBandwidth: 1,        // 1 Mbps
      maxResponseTime: 2000   // 2 seconds
    },
    healingThresholds: {
      consecutiveFailures: 3,
      criticalLatency: 500,   // 500ms
      criticalPacketLoss: 20  // 20%
    },
    enabled: true
  };

  constructor() {
    super();
    logger.info('🏥 Self-Healing Network Monitor initialized');
  }

  /**
   * Start network monitoring
   */
  public async start(): Promise<void> {
    try {
      if (this._isRunning) {
        logger.warn('Self-Healing Network Monitor is already running');
        return;
      }

      if (!this._config.enabled) {
        logger.info('Self-Healing Network Monitor is disabled');
        return;
      }

      logger.info('🚀 Starting Self-Healing Network Monitor...');

      // Perform initial health check
      await this.performHealthCheck();

      // Start continuous monitoring
      this._monitoringInterval = setInterval(
        this.performHealthCheck.bind(this),
        this._config.healthCheckInterval
      );

      this._isRunning = true;
      logger.info('✅ Self-Healing Network Monitor started successfully');
      logger.info(`⏰ Health checks every ${this._config.healthCheckInterval / 1000} seconds`);

    } catch (error) {
      logger.error('❌ Failed to start Self-Healing Network Monitor:', error);
      throw error;
    }
  }

  /**
   * Stop network monitoring
   */
  public async stop(): Promise<void> {
    try {
      if (!this._isRunning) {
        return;
      }

      logger.info('🛑 Stopping Self-Healing Network Monitor...');

      if (this._monitoringInterval) {
        clearInterval(this._monitoringInterval);
        this._monitoringInterval = null;
      }

      this._isRunning = false;
      logger.info('✅ Self-Healing Network Monitor stopped');

    } catch (error) {
      logger.error('❌ Error stopping Self-Healing Network Monitor:', error);
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      logger.debug('🔍 Performing network health check...');

      // Collect health metrics
      const metrics = await this.collectHealthMetrics();
      
      // Store metrics in history
      this._healthHistory.push(metrics);
      if (this._healthHistory.length > 1000) {
        this._healthHistory = this._healthHistory.slice(-1000);
      }

      // Analyze health and determine if healing is needed
      const healingNeeded = this.analyzeHealthMetrics(metrics);
      
      if (healingNeeded.length > 0) {
        logger.warn(`⚠️ Network health issues detected: ${healingNeeded.length} problems`);
        await this.performHealingActions(healingNeeded, metrics);
      } else {
        this._consecutiveFailures = 0;
        logger.debug('✅ Network health check passed');
      }

      this._lastHealthCheck = new Date();
      const checkDuration = Date.now() - startTime;
      
      // Emit health check event
      this.emit('healthCheck', {
        metrics,
        duration: checkDuration,
        healingActions: healingNeeded
      });

    } catch (error) {
      logger.error('❌ Health check failed:', error);
      this._consecutiveFailures++;
      
      // If health checks are consistently failing, try emergency healing
      if (this._consecutiveFailures >= this._config.healingThresholds.consecutiveFailures) {
        await this.performEmergencyHealing();
      }
    }
  }

  /**
   * Collect comprehensive health metrics
   */
  private async collectHealthMetrics(): Promise<NetworkHealthMetrics> {
    const networkConfig = hybridNetworkService.getNetworkConfiguration();
    
    // Test connectivity
    const connectivity = await this.testConnectivity(networkConfig);
    
    // Measure performance
    const performance = await this.measurePerformance(networkConfig);
    
    // Calculate stability metrics
    const stability = this.calculateStabilityMetrics();
    
    // Check service health
    const services = await this.checkServiceHealth();

    return {
      timestamp: new Date(),
      connectivity,
      performance,
      stability,
      services
    };
  }

  /**
   * Test network connectivity
   */
  private async testConnectivity(networkConfig: any): Promise<any> {
    const results = {
      staticIPReachable: false,
      dynamicIPReachable: false,
      internetReachable: false,
      gatewayReachable: false,
      dnsResolution: false
    };

    try {
      // Test static IP reachability
      if (networkConfig.staticIP) {
        results.staticIPReachable = await this.pingHost(networkConfig.staticIP);
      }

      // Test dynamic IP reachability
      if (networkConfig.dynamicIP) {
        results.dynamicIPReachable = await this.pingHost(networkConfig.dynamicIP);
      }

      // Test gateway reachability
      if (networkConfig.gateway) {
        results.gatewayReachable = await this.pingHost(networkConfig.gateway);
      }

      // Test internet connectivity
      results.internetReachable = await this.pingHost('*******');

      // Test DNS resolution
      results.dnsResolution = await this.testDNSResolution();

    } catch (error) {
      logger.debug('Connectivity test error:', error);
    }

    return results;
  }

  /**
   * Measure network performance
   */
  private async measurePerformance(networkConfig: any): Promise<any> {
    const performance = {
      latency: 0,
      packetLoss: 0,
      bandwidth: 0,
      responseTime: 0
    };

    try {
      // Measure latency to gateway
      if (networkConfig.gateway) {
        performance.latency = await this.measureLatency(networkConfig.gateway);
      }

      // Measure VMS server response time
      const startTime = Date.now();
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this._config.connectivityTimeout);

        const response = await fetch(`http://${networkConfig.currentIP}:${networkConfig.port}/health`, {
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        performance.responseTime = Date.now() - startTime;
      } catch {
        performance.responseTime = this._config.connectivityTimeout;
      }

      // Simplified packet loss test (ping multiple times)
      if (networkConfig.gateway) {
        performance.packetLoss = await this.measurePacketLoss(networkConfig.gateway);
      }

    } catch (error) {
      logger.debug('Performance measurement error:', error);
    }

    return performance;
  }

  /**
   * Calculate stability metrics
   */
  private calculateStabilityMetrics(): any {
    const recentHistory = this._healthHistory.slice(-20); // Last 20 checks
    
    let ipChanges = 0;
    let connectionDrops = 0;
    
    for (let i = 1; i < recentHistory.length; i++) {
      const prev = recentHistory[i - 1];
      const curr = recentHistory[i];
      
      // Count IP changes
      if (prev.connectivity.staticIPReachable !== curr.connectivity.staticIPReachable ||
          prev.connectivity.dynamicIPReachable !== curr.connectivity.dynamicIPReachable) {
        ipChanges++;
      }
      
      // Count connection drops
      if (prev.connectivity.internetReachable && !curr.connectivity.internetReachable) {
        connectionDrops++;
      }
    }

    const uptime = Date.now() - this._startTime.getTime();
    const recoveryTime = this._consecutiveFailures > 0 ? this._consecutiveFailures * this._config.healthCheckInterval : 0;

    return {
      ipChanges,
      connectionDrops,
      recoveryTime,
      uptime
    };
  }

  /**
   * Check service health
   */
  private async checkServiceHealth(): Promise<any> {
    return {
      vmsServerHealthy: await this.isVMSServerHealthy(),
      networkDiscoveryActive: networkDiscoveryService.isServiceRunning(),
      fallbackSystemActive: intelligentFallbackService.isRunning()
    };
  }

  /**
   * Analyze health metrics and determine healing needs
   */
  private analyzeHealthMetrics(metrics: NetworkHealthMetrics): string[] {
    const issues: string[] = [];

    // Check connectivity issues
    if (!metrics.connectivity.internetReachable) {
      issues.push('internet_connectivity');
    }
    
    if (!metrics.connectivity.gatewayReachable) {
      issues.push('gateway_connectivity');
    }
    
    if (!metrics.connectivity.dnsResolution) {
      issues.push('dns_resolution');
    }

    // Check performance issues
    if (metrics.performance.latency > this._config.performanceThresholds.maxLatency) {
      issues.push('high_latency');
    }
    
    if (metrics.performance.packetLoss > this._config.performanceThresholds.maxPacketLoss) {
      issues.push('packet_loss');
    }
    
    if (metrics.performance.responseTime > this._config.performanceThresholds.maxResponseTime) {
      issues.push('slow_response');
    }

    // Check service issues
    if (!metrics.services.vmsServerHealthy) {
      issues.push('vms_server_unhealthy');
    }
    
    if (!metrics.services.networkDiscoveryActive) {
      issues.push('network_discovery_inactive');
    }
    
    if (!metrics.services.fallbackSystemActive) {
      issues.push('fallback_system_inactive');
    }

    // Check critical thresholds
    if (metrics.performance.latency > this._config.healingThresholds.criticalLatency) {
      issues.push('critical_latency');
    }
    
    if (metrics.performance.packetLoss > this._config.healingThresholds.criticalPacketLoss) {
      issues.push('critical_packet_loss');
    }

    return issues;
  }

  /**
   * Perform healing actions
   */
  private async performHealingActions(issues: string[], metrics: NetworkHealthMetrics): Promise<void> {
    logger.info(`🏥 Performing healing actions for ${issues.length} issues...`);

    for (const issue of issues) {
      try {
        const action = await this.getHealingAction(issue);
        if (action) {
          const success = await this.executeHealingAction(action);
          
          const healingRecord: HealingAction = {
            id: `healing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: action.type,
            description: action.description,
            severity: action.severity,
            executedAt: new Date(),
            success,
            metrics
          };

          this._healingHistory.push(healingRecord);
          if (this._healingHistory.length > 500) {
            this._healingHistory = this._healingHistory.slice(-500);
          }

          this.emit('healingAction', healingRecord);

          if (success) {
            logger.info(`✅ Healing action successful: ${action.description}`);
          } else {
            logger.warn(`⚠️ Healing action failed: ${action.description}`);
          }
        }
      } catch (error) {
        logger.error(`❌ Error performing healing action for ${issue}:`, error);
      }
    }
  }

  /**
   * Get appropriate healing action for issue
   */
  private async getHealingAction(issue: string): Promise<any> {
    const actions: Record<string, any> = {
      'internet_connectivity': {
        type: 'switch_mode',
        description: 'Switch network mode due to connectivity issues',
        severity: 'high'
      },
      'gateway_connectivity': {
        type: 'reset_adapter',
        description: 'Reset network adapter due to gateway issues',
        severity: 'high'
      },
      'dns_resolution': {
        type: 'flush_dns',
        description: 'Flush DNS cache due to resolution issues',
        severity: 'medium'
      },
      'high_latency': {
        type: 'switch_mode',
        description: 'Switch network mode due to high latency',
        severity: 'medium'
      },
      'packet_loss': {
        type: 'switch_mode',
        description: 'Switch network mode due to packet loss',
        severity: 'medium'
      },
      'slow_response': {
        type: 'restart_service',
        description: 'Restart VMS server due to slow response',
        severity: 'medium'
      },
      'vms_server_unhealthy': {
        type: 'restart_service',
        description: 'Restart VMS server due to health check failure',
        severity: 'high'
      },
      'network_discovery_inactive': {
        type: 'restart_service',
        description: 'Restart network discovery service',
        severity: 'medium'
      },
      'fallback_system_inactive': {
        type: 'restart_service',
        description: 'Restart intelligent fallback system',
        severity: 'medium'
      },
      'critical_latency': {
        type: 'force_discovery',
        description: 'Force network discovery due to critical latency',
        severity: 'critical'
      },
      'critical_packet_loss': {
        type: 'force_discovery',
        description: 'Force network discovery due to critical packet loss',
        severity: 'critical'
      }
    };

    return actions[issue] || null;
  }

  /**
   * Execute healing action
   */
  private async executeHealingAction(action: any): Promise<boolean> {
    try {
      switch (action.type) {
        case 'restart_service':
          return await this.restartServices();
          
        case 'switch_mode':
          return await this.switchNetworkMode();
          
        case 'reset_adapter':
          return await this.resetNetworkAdapter();
          
        case 'flush_dns':
          return await this.flushDNSCache();
          
        case 'force_discovery':
          return await this.forceNetworkDiscovery();
          
        default:
          logger.warn(`Unknown healing action type: ${action.type}`);
          return false;
      }
    } catch (error) {
      logger.error(`Error executing healing action ${action.type}:`, error);
      return false;
    }
  }

  /**
   * Perform emergency healing when health checks consistently fail
   */
  private async performEmergencyHealing(): Promise<void> {
    logger.warn('🚨 Performing emergency healing due to consecutive failures...');

    try {
      // Try multiple recovery strategies
      await this.restartServices();
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await this.forceNetworkDiscovery();
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await this.switchNetworkMode();
      
      logger.info('🏥 Emergency healing completed');
      this._consecutiveFailures = 0;

    } catch (error) {
      logger.error('❌ Emergency healing failed:', error);
    }
  }

  // Helper methods for healing actions
  private async restartServices(): Promise<boolean> {
    try {
      await networkDiscoveryService.stop();
      await intelligentFallbackService.stop();
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      await networkDiscoveryService.start();
      await intelligentFallbackService.start();
      
      return true;
    } catch {
      return false;
    }
  }

  private async switchNetworkMode(): Promise<boolean> {
    try {
      const config = hybridNetworkService.getNetworkConfiguration();
      if (config.mode === 'static') {
        return await hybridNetworkService.switchToDynamicModeManual();
      } else {
        return await hybridNetworkService.switchToStaticMode();
      }
    } catch {
      return false;
    }
  }

  private async resetNetworkAdapter(): Promise<boolean> {
    // This would require platform-specific implementation
    logger.info('Network adapter reset requested (not implemented)');
    return true;
  }

  private async flushDNSCache(): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);
      
      if (process.platform === 'win32') {
        await execAsync('ipconfig /flushdns');
      } else {
        await execAsync('sudo systemctl restart systemd-resolved');
      }
      
      return true;
    } catch {
      return false;
    }
  }

  private async forceNetworkDiscovery(): Promise<boolean> {
    try {
      await networkDiscoveryService.stop();
      await new Promise(resolve => setTimeout(resolve, 2000));
      await networkDiscoveryService.start();
      return true;
    } catch {
      return false;
    }
  }

  // Helper methods for testing
  private async pingHost(host: string): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);
      
      const command = process.platform === 'win32' 
        ? `ping -n 1 -w 1000 ${host}`
        : `ping -c 1 -W 1 ${host}`;
      
      await execAsync(command);
      return true;
    } catch {
      return false;
    }
  }

  private async measureLatency(host: string): Promise<number> {
    const startTime = Date.now();
    const reachable = await this.pingHost(host);
    const endTime = Date.now();
    
    return reachable ? endTime - startTime : this._config.connectivityTimeout;
  }

  private async measurePacketLoss(host: string): Promise<number> {
    let successful = 0;
    const totalPings = 5;
    
    for (let i = 0; i < totalPings; i++) {
      if (await this.pingHost(host)) {
        successful++;
      }
    }
    
    return ((totalPings - successful) / totalPings) * 100;
  }

  private async testDNSResolution(): Promise<boolean> {
    try {
      const { lookup } = await import('dns');
      const { promisify } = await import('util');
      const lookupAsync = promisify(lookup);
      
      await lookupAsync('google.com');
      return true;
    } catch {
      return false;
    }
  }

  private async isVMSServerHealthy(): Promise<boolean> {
    try {
      const config = hybridNetworkService.getNetworkConfiguration();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this._config.connectivityTimeout);

      const response = await fetch(`http://${config.currentIP}:${config.port}/health`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Get monitoring statistics
   */
  public getStats(): any {
    return {
      isRunning: this._isRunning,
      uptime: Date.now() - this._startTime.getTime(),
      totalHealthChecks: this._healthHistory.length,
      totalHealingActions: this._healingHistory.length,
      consecutiveFailures: this._consecutiveFailures,
      lastHealthCheck: this._lastHealthCheck,
      recentHealth: this._healthHistory.slice(-10),
      recentHealing: this._healingHistory.slice(-10)
    };
  }

  /**
   * Update monitoring configuration
   */
  public updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this._config = { ...this._config, ...newConfig };
    logger.info('Self-Healing Network Monitor configuration updated');
  }

  /**
   * Check if monitor is running
   */
  public isRunning(): boolean {
    return this._isRunning;
  }
}

// Export singleton instance
export const selfHealingNetworkMonitor = new SelfHealingNetworkMonitor();
