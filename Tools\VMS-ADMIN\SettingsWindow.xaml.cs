using System;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Microsoft.Win32;

namespace VMSADMIN
{
    /// <summary>
    /// VMS-ADMIN Settings Window
    /// Configuration interface for VMS administration client
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private readonly string _settingsPath;
        private readonly string _logPath;

        public SettingsWindow()
        {
            InitializeComponent();
            
            // Initialize paths
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "VMS-ADMIN");
            Directory.CreateDirectory(appDataPath);
            
            _settingsPath = Path.Combine(appDataPath, "settings.ini");
            _logPath = Path.Combine(appDataPath, "logs");
            Directory.CreateDirectory(_logPath);
            
            LoadSettings();
        }

        /// <summary>
        /// Load settings from file
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var lines = File.ReadAllLines(_settingsPath);
                    foreach (var line in lines)
                    {
                        if (line.Contains("="))
                        {
                            var parts = line.Split('=');
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();
                            
                            ApplySetting(key, value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}", "Settings Error", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// Apply individual setting
        /// </summary>
        private void ApplySetting(string key, string value)
        {
            try
            {
                switch (key.ToLower())
                {
                    case "discoverytimeout":
                        DiscoveryTimeoutTextBox.Text = value;
                        break;
                    case "refreshinterval":
                        RefreshIntervalTextBox.Text = value;
                        break;
                    case "autoconnect":
                        AutoConnectCheckBox.IsChecked = bool.Parse(value);
                        break;
                    case "manualserverip":
                        ManualServerIPTextBox.Text = value;
                        break;
                    case "adminport":
                        AdminPortTextBox.Text = value;
                        break;
                    case "connectiontimeout":
                        ConnectionTimeoutTextBox.Text = value;
                        break;
                    case "minimizetotray":
                        MinimizeToTrayCheckBox.IsChecked = bool.Parse(value);
                        break;
                    case "startwithwindows":
                        StartWithWindowsCheckBox.IsChecked = bool.Parse(value);
                        break;
                    case "shownotifications":
                        ShowNotificationsCheckBox.IsChecked = bool.Parse(value);
                        break;
                    case "autocloseafterconnect":
                        AutoCloseAfterConnectCheckBox.IsChecked = bool.Parse(value);
                        break;
                    case "loglevel":
                        if (int.TryParse(value, out int logLevel) && logLevel >= 0 && logLevel < LogLevelComboBox.Items.Count)
                        {
                            LogLevelComboBox.SelectedIndex = logLevel;
                        }
                        break;
                    case "enablelogging":
                        EnableLoggingCheckBox.IsChecked = bool.Parse(value);
                        break;
                }
            }
            catch (Exception ex)
            {
                // Ignore individual setting errors
                Debug.WriteLine($"Error applying setting {key}={value}: {ex.Message}");
            }
        }

        /// <summary>
        /// Save settings to file
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settings = new[]
                {
                    $"DiscoveryTimeout={DiscoveryTimeoutTextBox.Text}",
                    $"RefreshInterval={RefreshIntervalTextBox.Text}",
                    $"AutoConnect={AutoConnectCheckBox.IsChecked}",
                    $"ManualServerIP={ManualServerIPTextBox.Text}",
                    $"AdminPort={AdminPortTextBox.Text}",
                    $"ConnectionTimeout={ConnectionTimeoutTextBox.Text}",
                    $"MinimizeToTray={MinimizeToTrayCheckBox.IsChecked}",
                    $"StartWithWindows={StartWithWindowsCheckBox.IsChecked}",
                    $"ShowNotifications={ShowNotificationsCheckBox.IsChecked}",
                    $"AutoCloseAfterConnect={AutoCloseAfterConnectCheckBox.IsChecked}",
                    $"LogLevel={LogLevelComboBox.SelectedIndex}",
                    $"EnableLogging={EnableLoggingCheckBox.IsChecked}",
                    $"LastSaved={DateTime.Now:yyyy-MM-dd HH:mm:ss}"
                };

                File.WriteAllLines(_settingsPath, settings);
                
                // Handle Windows startup setting
                HandleWindowsStartup();
                
                MessageBox.Show("Settings saved successfully!", "Settings", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Settings Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle Windows startup registry setting
        /// </summary>
        private void HandleWindowsStartup()
        {
            try
            {
                const string keyName = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
                const string appName = "VMS-ADMIN";
                
                using (var key = Registry.CurrentUser.OpenSubKey(keyName, true))
                {
                    if (StartWithWindowsCheckBox.IsChecked == true)
                    {
                        // Add to startup
                        var exePath = Process.GetCurrentProcess().MainModule?.FileName;
                        if (!string.IsNullOrEmpty(exePath))
                        {
                            key?.SetValue(appName, $"\"{exePath}\" --startup");
                        }
                    }
                    else
                    {
                        // Remove from startup
                        key?.DeleteValue(appName, false);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Warning: Could not update Windows startup setting: {ex.Message}", 
                    "Startup Setting", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// Reset settings to defaults
        /// </summary>
        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to reset all settings to defaults?", 
                "Reset Settings", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // Reset to default values
                DiscoveryTimeoutTextBox.Text = "10";
                RefreshIntervalTextBox.Text = "30";
                AutoConnectCheckBox.IsChecked = false;
                ManualServerIPTextBox.Text = "";
                AdminPortTextBox.Text = "8081";
                ConnectionTimeoutTextBox.Text = "10";
                MinimizeToTrayCheckBox.IsChecked = false;
                StartWithWindowsCheckBox.IsChecked = false;
                ShowNotificationsCheckBox.IsChecked = true;
                AutoCloseAfterConnectCheckBox.IsChecked = true;
                LogLevelComboBox.SelectedIndex = 1; // Info
                EnableLoggingCheckBox.IsChecked = true;
            }
        }

        /// <summary>
        /// Cancel and close window
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// Open log folder in Windows Explorer
        /// </summary>
        private void OpenLogFolderButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Directory.Exists(_logPath))
                {
                    Process.Start("explorer.exe", _logPath);
                }
                else
                {
                    MessageBox.Show("Log folder does not exist yet. Logs will be created when VMS-ADMIN runs.", 
                        "Log Folder", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening log folder: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Clear all log files
        /// </summary>
        private void ClearLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to delete all log files?", 
                    "Clear Logs", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    if (Directory.Exists(_logPath))
                    {
                        var logFiles = Directory.GetFiles(_logPath, "*.log");
                        foreach (var file in logFiles)
                        {
                            File.Delete(file);
                        }
                        
                        MessageBox.Show($"Deleted {logFiles.Length} log files.", "Clear Logs", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("No log files found.", "Clear Logs", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing logs: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Get current settings as configuration object
        /// </summary>
        public VMSAdminSettings GetSettings()
        {
            return new VMSAdminSettings
            {
                DiscoveryTimeout = int.TryParse(DiscoveryTimeoutTextBox.Text, out int dt) ? dt : 10,
                RefreshInterval = int.TryParse(RefreshIntervalTextBox.Text, out int ri) ? ri : 30,
                AutoConnect = AutoConnectCheckBox.IsChecked ?? false,
                ManualServerIP = ManualServerIPTextBox.Text?.Trim(),
                AdminPort = int.TryParse(AdminPortTextBox.Text, out int ap) ? ap : 8081,
                ConnectionTimeout = int.TryParse(ConnectionTimeoutTextBox.Text, out int ct) ? ct : 10,
                MinimizeToTray = MinimizeToTrayCheckBox.IsChecked ?? false,
                StartWithWindows = StartWithWindowsCheckBox.IsChecked ?? false,
                ShowNotifications = ShowNotificationsCheckBox.IsChecked ?? true,
                AutoCloseAfterConnect = AutoCloseAfterConnectCheckBox.IsChecked ?? true,
                LogLevel = LogLevelComboBox.SelectedIndex,
                EnableLogging = EnableLoggingCheckBox.IsChecked ?? true
            };
        }
    }

    /// <summary>
    /// VMS-ADMIN settings configuration
    /// </summary>
    public class VMSAdminSettings
    {
        public int DiscoveryTimeout { get; set; } = 10;
        public int RefreshInterval { get; set; } = 30;
        public bool AutoConnect { get; set; } = false;
        public string ManualServerIP { get; set; } = "";
        public int AdminPort { get; set; } = 8081;
        public int ConnectionTimeout { get; set; } = 10;
        public bool MinimizeToTray { get; set; } = false;
        public bool StartWithWindows { get; set; } = false;
        public bool ShowNotifications { get; set; } = true;
        public bool AutoCloseAfterConnect { get; set; } = true;
        public int LogLevel { get; set; } = 1; // Info
        public bool EnableLogging { get; set; } = true;
    }
}
