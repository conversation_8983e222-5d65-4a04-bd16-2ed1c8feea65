/**
 * Network Discovery Service
 * Implements UDP broadcast system for dynamic IP discovery and client auto-configuration
 * Handles server announcement and client discovery for automated deployment
 */

import dgram from 'dgram';
import os from 'os';
import { logger } from '../utils/logger.js';

export interface NetworkDiscoveryConfig {
  broadcastPort: number;
  discoveryPort: number;
  serviceName: string;
  announceInterval: number;
  enabled: boolean;
}

export interface ServerAnnouncement {
  serviceName: string;
  serverIP: string;
  serverPort: number;
  version: string;
  timestamp: number;
  capabilities: string[];
  deploymentMode: string;
}

export class NetworkDiscoveryService {
  private broadcastSocket: dgram.Socket | null = null;
  private discoverySocket: dgram.Socket | null = null;
  private announceTimer: NodeJS.Timeout | null = null;
  private isRunning = false;

  private config: NetworkDiscoveryConfig = {
    broadcastPort: 8081,
    discoveryPort: 8082,
    serviceName: 'VMS-Production',
    announceInterval: 30000, // 30 seconds
    enabled: true
  };

  private serverInfo: ServerAnnouncement;

  constructor(serverPort: number = 8080) {
    this.serverInfo = {
      serviceName: this.config.serviceName,
      serverIP: this.getLocalIPAddress(),
      serverPort: serverPort,
      version: '1.0.0',
      timestamp: Date.now(),
      capabilities: ['voucher-management', 'real-time-updates', 'multi-user', 'automated-deployment'],
      deploymentMode: process.env.VMS_DEPLOYMENT_MODE || 'standard'
    };

    logger.info(`Network Discovery Service initialized for ${this.serverInfo.serverIP}:${serverPort}`);
  }

  /**
   * Start network discovery service
   */
  public async start(): Promise<void> {
    try {
      if (this.isRunning) {
        logger.warn('Network Discovery Service is already running');
        return;
      }

      logger.info('🌐 Starting Network Discovery Service...');

      // Start broadcast socket for announcing server
      await this.startBroadcastSocket();

      // Start discovery socket for responding to client queries
      await this.startDiscoverySocket();

      // Start periodic announcements
      this.startPeriodicAnnouncements();

      this.isRunning = true;
      logger.info(`✅ Network Discovery Service started successfully`);
      logger.info(`📡 Broadcasting on port ${this.config.broadcastPort}`);
      logger.info(`🔍 Discovery listening on port ${this.config.discoveryPort}`);
      logger.info(`🖥️ Server announced as: ${this.serverInfo.serverIP}:${this.serverInfo.serverPort}`);

    } catch (error) {
      logger.error('❌ Failed to start Network Discovery Service:', error);
      throw error;
    }
  }

  /**
   * Stop network discovery service
   */
  public async stop(): Promise<void> {
    try {
      if (!this.isRunning) {
        return;
      }

      logger.info('🛑 Stopping Network Discovery Service...');

      // Stop periodic announcements
      if (this.announceTimer) {
        clearInterval(this.announceTimer);
        this.announceTimer = null;
      }

      // Close sockets
      if (this.broadcastSocket) {
        this.broadcastSocket.close();
        this.broadcastSocket = null;
      }

      if (this.discoverySocket) {
        this.discoverySocket.close();
        this.discoverySocket = null;
      }

      this.isRunning = false;
      logger.info('✅ Network Discovery Service stopped');

    } catch (error) {
      logger.error('❌ Error stopping Network Discovery Service:', error);
    }
  }

  /**
   * Start broadcast socket for server announcements
   */
  private async startBroadcastSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.broadcastSocket = dgram.createSocket('udp4');

        this.broadcastSocket.on('error', (error) => {
          logger.error('Broadcast socket error:', error);
          reject(error);
        });

        this.broadcastSocket.bind(() => {
          this.broadcastSocket!.setBroadcast(true);
          logger.info(`📡 Broadcast socket bound and ready`);
          resolve();
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Start discovery socket for client queries
   */
  private async startDiscoverySocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.discoverySocket = dgram.createSocket('udp4');

        this.discoverySocket.on('error', (error) => {
          logger.error('Discovery socket error:', error);
          reject(error);
        });

        this.discoverySocket.on('message', (message, remoteInfo) => {
          this.handleDiscoveryRequest(message, remoteInfo);
        });

        this.discoverySocket.bind(this.config.discoveryPort, () => {
          logger.info(`🔍 Discovery socket listening on port ${this.config.discoveryPort}`);
          resolve();
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle discovery request from client
   */
  private handleDiscoveryRequest(message: Buffer, remoteInfo: dgram.RemoteInfo): void {
    try {
      const request = JSON.parse(message.toString());
      
      if (request.type === 'VMS_DISCOVERY_REQUEST') {
        logger.info(`🔍 Discovery request from ${remoteInfo.address}:${remoteInfo.port}`);
        
        // Update server info with current timestamp
        this.serverInfo.timestamp = Date.now();
        this.serverInfo.serverIP = this.getLocalIPAddress(); // Update in case IP changed
        
        const response = {
          type: 'VMS_DISCOVERY_RESPONSE',
          server: this.serverInfo,
          respondedAt: Date.now()
        };

        const responseBuffer = Buffer.from(JSON.stringify(response));
        
        this.discoverySocket!.send(responseBuffer, remoteInfo.port, remoteInfo.address, (error) => {
          if (error) {
            logger.error('Error sending discovery response:', error);
          } else {
            logger.info(`📤 Discovery response sent to ${remoteInfo.address}:${remoteInfo.port}`);
          }
        });
      }
    } catch (error) {
      logger.warn('Invalid discovery request received:', error);
    }
  }

  /**
   * Start periodic server announcements
   */
  private startPeriodicAnnouncements(): void {
    // Send initial announcement immediately
    this.broadcastServerAnnouncement();

    // Schedule periodic announcements
    this.announceTimer = setInterval(() => {
      this.broadcastServerAnnouncement();
    }, this.config.announceInterval);

    logger.info(`⏰ Periodic announcements scheduled every ${this.config.announceInterval / 1000} seconds`);
  }

  /**
   * Broadcast server announcement
   */
  private broadcastServerAnnouncement(): void {
    try {
      if (!this.broadcastSocket) {
        return;
      }

      // Update server info with current timestamp and IP
      this.serverInfo.timestamp = Date.now();
      this.serverInfo.serverIP = this.getLocalIPAddress();

      const announcement = {
        type: 'VMS_SERVER_ANNOUNCEMENT',
        server: this.serverInfo
      };

      const message = Buffer.from(JSON.stringify(announcement));
      
      // Broadcast to all network interfaces
      const broadcastAddresses = this.getBroadcastAddresses();
      
      broadcastAddresses.forEach(address => {
        this.broadcastSocket!.send(message, this.config.broadcastPort, address, (error) => {
          if (error) {
            logger.debug(`Broadcast error to ${address}:`, error.message);
          } else {
            logger.debug(`📡 Server announced to ${address}:${this.config.broadcastPort}`);
          }
        });
      });

    } catch (error) {
      logger.error('Error broadcasting server announcement:', error);
    }
  }

  /**
   * Get local IP address
   */
  private getLocalIPAddress(): string {
    try {
      const interfaces = os.networkInterfaces();
      
      // Prefer Ethernet, then WiFi, then others
      const preferredTypes = ['Ethernet', 'Wi-Fi', 'WiFi', 'Wireless'];
      
      for (const type of preferredTypes) {
        for (const [name, addresses] of Object.entries(interfaces)) {
          if (name.toLowerCase().includes(type.toLowerCase()) && addresses) {
            for (const addr of addresses) {
              if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                return addr.address;
              }
            }
          }
        }
      }

      // Fallback: any non-internal IPv4 address
      for (const addresses of Object.values(interfaces)) {
        if (addresses) {
          for (const addr of addresses) {
            if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
              return addr.address;
            }
          }
        }
      }

      return 'localhost';
    } catch (error) {
      logger.warn('Error getting local IP address:', error);
      return 'localhost';
    }
  }

  /**
   * Get broadcast addresses for all network interfaces
   */
  private getBroadcastAddresses(): string[] {
    try {
      const interfaces = os.networkInterfaces();
      const broadcastAddresses: string[] = [];

      for (const addresses of Object.values(interfaces)) {
        if (addresses) {
          for (const addr of addresses) {
            if (addr.family === 'IPv4' && !addr.internal) {
              // Calculate broadcast address
              const ip = addr.address.split('.').map(Number);
              const netmask = addr.netmask.split('.').map(Number);
              
              const broadcast = ip.map((octet, index) => 
                octet | (255 - netmask[index])
              ).join('.');
              
              if (!broadcastAddresses.includes(broadcast)) {
                broadcastAddresses.push(broadcast);
              }
            }
          }
        }
      }

      // Add common broadcast addresses as fallback
      if (broadcastAddresses.length === 0) {
        broadcastAddresses.push('***************');
      }

      return broadcastAddresses;
    } catch (error) {
      logger.warn('Error getting broadcast addresses:', error);
      return ['***************'];
    }
  }

  /**
   * Update server configuration
   */
  public updateConfig(newConfig: Partial<NetworkDiscoveryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Network Discovery Service configuration updated');
  }

  /**
   * Get current server information
   */
  public getServerInfo(): ServerAnnouncement {
    return { ...this.serverInfo };
  }

  /**
   * Check if service is running
   */
  public isServiceRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get network statistics
   */
  public getNetworkStats(): any {
    return {
      isRunning: this.isRunning,
      serverIP: this.serverInfo.serverIP,
      serverPort: this.serverInfo.serverPort,
      broadcastPort: this.config.broadcastPort,
      discoveryPort: this.config.discoveryPort,
      announceInterval: this.config.announceInterval,
      lastAnnouncement: this.serverInfo.timestamp,
      broadcastAddresses: this.getBroadcastAddresses()
    };
  }
}

// Export singleton instance
export const networkDiscoveryService = new NetworkDiscoveryService();
