declare class ServerConnectionManager {
    private disconnectionTimers;
    private readonly GRACE_PERIOD;
    private readonly HEALTH_CHECK_THROTTLE;
    private lastHealthCheck;
    handleDisconnection(userId: string, userName: string, department: string, socketId: string, cleanupCallback: () => Promise<void>): void;
    cancelDisconnectionTimer(timerKey: string): boolean;
    handleReconnection(userId: string, userName: string, socketId: string): void;
    shouldAllowHealthCheck(clientId: string): boolean;
    cleanupHealthCheckRecords(): void;
    getConnectionStats(): {
        pendingDisconnections: number;
        healthCheckClients: number;
        gracePeriodMs: number;
        healthCheckThrottleMs: number;
    };
    cleanup(): void;
}
export declare const serverConnectionManager: ServerConnectionManager;
export declare function healthCheckMiddleware(req: any, res: any, next: any): any;
export {};
