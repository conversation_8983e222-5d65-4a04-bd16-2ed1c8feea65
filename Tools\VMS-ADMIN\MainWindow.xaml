<Window x:Class="VMSADMIN.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="VMS-ADMIN - System Administration"
        Height="550" Width="750"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"

        Background="#F5F5F5">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="StatusStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="Background" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="#757575"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="15,6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#9E9E9E"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Background="White" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15">
                <Border Background="#1565C0" Width="48" Height="48" CornerRadius="8" Margin="0,0,15,0">
                    <TextBlock Text="VMS" Foreground="White" FontSize="16" FontWeight="Bold"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="VMS-ADMIN" Style="{StaticResource HeaderStyle}" Margin="0"/>
                    <TextBlock Text="System Administration Client" Style="{StaticResource SubHeaderStyle}" Margin="0"/>
                </StackPanel>
            </StackPanel>
        </StackPanel>
        
        <!-- Status Display -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="20,0">
            <StackPanel Margin="20">
                <TextBlock x:Name="StatusText" Text="Initializing VMS-ADMIN..." 
                          Style="{StaticResource StatusStyle}" Foreground="#1565C0"/>
                
                <ProgressBar x:Name="ProgressBar" Height="8" Margin="20,10" 
                            Minimum="0" Maximum="100" Value="0" 
                            Background="#E0E0E0" Foreground="#1565C0"/>
                
                <TextBlock x:Name="DetailText" Text="Preparing to connect to VMS server..." 
                          FontSize="12" Foreground="#757575" HorizontalAlignment="Center" Margin="0,5"/>
            </StackPanel>
        </Border>
        
        <!-- Connection Info -->
        <StackPanel Grid.Row="2" Margin="20" VerticalAlignment="Center">
            <Border Background="#E3F2FD" BorderBrush="#1565C0" BorderThickness="1" Padding="15" Margin="0,10">
                <StackPanel>
                    <TextBlock Text="🌐 Network Discovery Status" FontWeight="Bold" FontSize="14" Foreground="#1565C0" Margin="0,0,0,10"/>
                    <TextBlock x:Name="NetworkStatusText" Text="Scanning for VMS server..." FontSize="12" Foreground="#424242" Margin="0,2"/>
                    <TextBlock x:Name="ServerInfoText" Text="Server: Not detected" FontSize="12" Foreground="#424242" Margin="0,2"/>
                    <TextBlock x:Name="AdminServiceText" Text="Admin Service: Checking..." FontSize="12" Foreground="#424242" Margin="0,2"/>
                </StackPanel>
            </Border>
        </StackPanel>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="20,10">
            <Button x:Name="ConnectButton" Content="🚀 Connect to Dashboard" 
                   Style="{StaticResource ActionButton}" Click="ConnectButton_Click" IsEnabled="False"/>
            <Button x:Name="RefreshButton" Content="🔄 Refresh" 
                   Style="{StaticResource SecondaryButton}" Click="RefreshButton_Click"/>
            <Button x:Name="SettingsButton" Content="⚙️ Settings" 
                   Style="{StaticResource SecondaryButton}" Click="SettingsButton_Click"/>
        </StackPanel>
        
        <!-- Footer -->
        <Border Grid.Row="4" Background="#F5F5F5" Padding="15,10">
            <StackPanel>
                <TextBlock Text="VMS-ADMIN v1.0 - System Administration Client" 
                          FontSize="11" Foreground="#757575" HorizontalAlignment="Center"/>
                <TextBlock x:Name="FooterStatusText" Text="Ready to connect to VMS administration dashboard" 
                          FontSize="10" Foreground="#9E9E9E" HorizontalAlignment="Center" Margin="0,3,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
